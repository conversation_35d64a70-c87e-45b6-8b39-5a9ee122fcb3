# Admin Service

The Admin Service provides administrative functionality for the AgriTech seller platform.

## Features

- Database seeding
- Health monitoring
- API documentation with Swagger

## API Documentation

The service includes Swagger documentation accessible at `/api/docs` when the service is running.

### Accessing Swagger Documentation

1. Start the admin service
2. Open your browser and navigate to `http://localhost:3333/api/docs`

### Adding Documentation to Endpoints

To document new endpoints, add JSDoc comments above your route handlers:

```javascript
/**
 * @swagger
 * /api/your-endpoint:
 *   get:
 *     summary: Brief description
 *     description: Detailed description
 *     responses:
 *       200:
 *         description: Success response
 */
app.get('/api/your-endpoint', (req, res) => {
  // Your implementation
});
```

### Models/Schemas

Common models and schemas are defined in `src/models/swagger-models.ts`.

## Development

### Prerequisites

- Node.js
- MongoDB

### Setup

```bash
# Install dependencies
npm install

# Start the service
nx serve admin-service
```
