import { Request, Response } from 'express';
import { dbManager } from '../../../../libs/shared/database-connectors/db-manager';
import { SellerRepository } from '../../../../libs/shared/database-connectors/repositories/mongo/seller.repository';
import { FarmRepository } from '../../../../libs/shared/database-connectors/repositories/mongo/farm.repository';
import { SellerOnboardingService } from '../../../../apps/seller-service/src/services/seller-onboarding.service';
import { RegisterSellerDto } from '../../../../apps/seller-service/src/dto/seller.dto';
import { AdminModel } from '../../../../libs/shared/database-connectors/schemas/mongo/admin.schema';

export class UserManagementController {
  private sellerRepository: SellerRepository;
  private farmRepository: FarmRepository;

  constructor() {
    const mongoConnector = dbManager.getMongoDBConnector();
    this.sellerRepository = new SellerRepository(mongoConnector);
    this.farmRepository = new FarmRepository(mongoConnector);
  }

  /**
   * Get all users (admins and sellers combined)
   */
  getAllUsers = async (req: Request, res: Response): Promise<void> => {
    try {
      const { page = 1, limit = 10, role, status } = req.query;
      const pageNum = parseInt(page as string);
      const limitNum = parseInt(limit as string);

      // Get admins
      const adminQuery: any = {};
      if (role && role !== 'SELLER') {
        adminQuery['personalInfo.role'] = role;
      }

      const admins = await AdminModel.find(adminQuery)
        .select('-password')
        .limit(limitNum)
        .skip((pageNum - 1) * limitNum);

      // Get sellers if role is not specified or is SELLER
      let sellers: any[] = [];
      if (!role || role === 'SELLER') {
        const sellerQuery: any = {};
        if (status) {
          sellerQuery.status = status;
        }
        const sellerResult = await this.sellerRepository.findAll(pageNum, limitNum);
        sellers = sellerResult.sellers.filter(seller => !status || seller.status === status);
      }

      // Combine and format users
      const users = [
        ...admins.map(admin => ({
          id: admin.adminId,
          type: 'ADMIN',
          name: admin.personalInfo.name,
          email: admin.personalInfo.email,
          role: admin.personalInfo.role,
          status: 'ACTIVE',
          lastLogin: admin.activity.lastLogin,
          createdAt: admin.createdAt
        })),
        ...sellers.map(seller => ({
          id: seller.sellerId,
          type: 'SELLER',
          name: seller.personalInfo.name,
          email: seller.personalInfo.email,
          role: 'SELLER',
          status: seller.status,
          lastLogin: seller.activity?.lastLogin,
          createdAt: seller.createdAt
        }))
      ];

      const total = admins.length + sellers.length;

      res.status(200).json({
        success: true,
        data: {
          users,
          pagination: {
            total,
            page: pageNum,
            limit: limitNum,
            totalPages: Math.ceil(total / limitNum)
          }
        }
      });
    } catch (error) {
      console.error('Error getting all users:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Failed to fetch users'
        }
      });
    }
  };

  /**
   * Get user by ID
   */
  getUserById = async (req: Request, res: Response): Promise<void> => {
    try {
      const { userId } = req.params;

      // Try to find admin first
      let user = await AdminModel.findOne({ adminId: userId }).select('-password');

      if (user) {
        res.status(200).json({
          success: true,
          data: {
            id: user.adminId,
            type: 'ADMIN',
            name: user.personalInfo.name,
            email: user.personalInfo.email,
            role: user.personalInfo.role,
            status: 'ACTIVE',
            lastLogin: user.activity.lastLogin,
            permissions: user.personalInfo.permissions,
            createdAt: user.createdAt
          }
        });
        return;
      }

      // Try to find seller
      const seller = await this.sellerRepository.findById(userId);
      if (seller) {
        res.status(200).json({
          success: true,
          data: {
            id: seller.sellerId,
            type: 'SELLER',
            name: seller.personalInfo.name,
            email: seller.personalInfo.email,
            role: 'SELLER',
            status: seller.status,
            verificationStatus: seller.verificationStatus,
            lastLogin: null, // Seller schema doesn't have lastLogin
            createdAt: seller.createdAt
          }
        });
        return;
      }

      res.status(404).json({
        success: false,
        error: {
          code: 'USER_NOT_FOUND',
          message: 'User not found'
        }
      });
    } catch (error) {
      console.error('Error getting user:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Failed to get user'
        }
      });
    }
  };

  /**
   * Update user status
   */
  updateUserStatus = async (req: Request, res: Response): Promise<void> => {
    try {
      const { userId } = req.params;
      const { status } = req.body;
      
      res.status(200).json({
        success: true,
        data: {
          message: `User ${userId} status updated to ${status}`,
          timestamp: new Date().toISOString()
        }
      });
    } catch (error) {
      console.error('Error updating user status:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Failed to update user status'
        }
      });
    }
  };

  /**
   * Delete user
   */
  deleteUser = async (req: Request, res: Response): Promise<void> => {
    try {
      const { userId } = req.params;
      
      res.status(200).json({
        success: true,
        data: {
          message: `User ${userId} deleted successfully`,
          timestamp: new Date().toISOString()
        }
      });
    } catch (error) {
      console.error('Error deleting user:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Failed to delete user'
        }
      });
    }
  };

  /**
   * Get all sellers
   */
  getAllSellers = async (req: Request, res: Response): Promise<void> => {
    try {
      const { page = 1, limit = 10 } = req.query;
      const mongoConnector = dbManager.getMongoDBConnector();
      if (!mongoConnector) {
        res.status(500).json({
          success: false,
          error: {
            code: 'DB_NOT_CONNECTED',
            message: 'MongoDB connector not initialized.'
          }
        });
        return;
      }
      const sellerRepo = new SellerRepository(mongoConnector);
      const { sellers, total, pages } = await sellerRepo.findAll(Number(page), Number(limit));
      res.status(200).json({
        success: true,
        data: {
          sellers,
          total,
          page: Number(page),
          totalPages: pages
        }
      });
    } catch (error) {
      console.error('Error getting sellers:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Failed to get sellers'
        }
      });
    }
  };

  /**
   * Get seller by ID
   */
  getSellerById = async (req: Request, res: Response): Promise<void> => {
    try {
      const { sellerId } = req.params;

      const seller = await this.sellerRepository.findById(sellerId);

      if (!seller) {
        res.status(404).json({
          success: false,
          error: {
            code: 'SELLER_NOT_FOUND',
            message: 'Seller not found'
          }
        });
        return;
      }

      res.status(200).json({
        success: true,
        data: {
          id: seller.sellerId,
          name: seller.personalInfo.name,
          email: seller.personalInfo.email,
          contact: seller.personalInfo.contact,
          address: seller.personalInfo.address,
          status: seller.status,
          verificationStatus: seller.verificationStatus,
          documents: seller.documents,
          bankDetails: seller.bankDetails,
          farms: seller.farms,
          statusHistory: seller.statusHistory,
          createdAt: seller.createdAt,
          updatedAt: seller.updatedAt
        }
      });
    } catch (error) {
      console.error('Error getting seller:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Failed to get seller'
        }
      });
    }
  };

  /**
   * Verify seller
   */
  verifySeller = async (req: Request, res: Response): Promise<void> => {
    try {
      const { sellerId } = req.params;
      const { approved, notes, adminId } = req.body;

      const seller = await this.sellerRepository.findById(sellerId);

      if (!seller) {
        res.status(404).json({
          success: false,
          error: {
            code: 'SELLER_NOT_FOUND',
            message: 'Seller not found'
          }
        });
        return;
      }

      const newStatus = approved ? 'VERIFIED' : 'REJECTED';
      const newSellerStatus = approved ? 'ACTIVE' : 'SUSPENDED';

      // Update seller verification status and status
      const updatedSeller = await this.sellerRepository.update(sellerId, {
        verificationStatus: newStatus,
        status: newSellerStatus,
        statusHistory: [
          ...seller.statusHistory,
          {
            status: newStatus,
            updatedBy: adminId || 'ADMIN',
            reason: notes || `Seller ${approved ? 'verified' : 'rejected'} by admin`,
            updatedAt: new Date()
          }
        ]
      });

      res.status(200).json({
        success: true,
        data: {
          message: `Seller ${approved ? 'verified' : 'rejected'} successfully`,
          seller: updatedSeller,
          timestamp: new Date().toISOString()
        }
      });
    } catch (error) {
      console.error('Error verifying seller:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Failed to verify seller'
        }
      });
    }
  };

  /**
   * Update seller status
   */
  updateSellerStatus = async (req: Request, res: Response): Promise<void> => {
    try {
      const { sellerId } = req.params;
      const { status, reason, adminId } = req.body;

      const seller = await this.sellerRepository.findById(sellerId);

      if (!seller) {
        res.status(404).json({
          success: false,
          error: {
            code: 'SELLER_NOT_FOUND',
            message: 'Seller not found'
          }
        });
        return;
      }

      // Update seller status with history
      const updatedSeller = await this.sellerRepository.update(sellerId, {
        status,
        statusHistory: [
          ...seller.statusHistory,
          {
            status,
            updatedBy: adminId || 'ADMIN',
            reason: reason || `Status updated to ${status}`,
            updatedAt: new Date()
          }
        ]
      });

      res.status(200).json({
        success: true,
        data: {
          message: `Seller status updated to ${status}`,
          seller: updatedSeller,
          timestamp: new Date().toISOString()
        }
      });
    } catch (error) {
      console.error('Error updating seller status:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Failed to update seller status'
        }
      });
    }
  };

  /**
   * Update seller details
   */
  updateSeller = async (req: Request, res: Response): Promise<void> => {
    try {
      const { sellerId } = req.params;
      const updateData = req.body;

      const seller = await this.sellerRepository.findById(sellerId);

      if (!seller) {
        res.status(404).json({
          success: false,
          error: {
            code: 'SELLER_NOT_FOUND',
            message: 'Seller not found'
          }
        });
        return;
      }

      // Remove fields that shouldn't be updated directly
      delete updateData.sellerId;
      delete updateData.createdAt;
      delete updateData.password;

      const updatedSeller = await this.sellerRepository.update(sellerId, updateData);

      res.status(200).json({
        success: true,
        data: {
          message: 'Seller updated successfully',
          seller: updatedSeller
        }
      });
    } catch (error) {
      console.error('Error updating seller:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Failed to update seller'
        }
      });
    }
  };

  /**
   * Get all farms
   */
  getAllFarms = async (req: Request, res: Response): Promise<void> => {
    try {
      const { page = 1, limit = 10, sellerId, status } = req.query;
      const pageNum = parseInt(page as string);
      const limitNum = parseInt(limit as string);

      const { farms } = await this.farmRepository.findAll(pageNum, limitNum);

      // Apply filters if provided
      let filteredFarms = farms;
      if (sellerId) {
        filteredFarms = filteredFarms.filter(farm => farm.sellerId === sellerId);
      }
      if (status) {
        filteredFarms = filteredFarms.filter(farm => farm.status === status);
      }

      res.status(200).json({
        success: true,
        data: {
          farms: filteredFarms,
          pagination: {
            total: filteredFarms.length,
            page: pageNum,
            limit: limitNum,
            totalPages: Math.ceil(filteredFarms.length / limitNum)
          }
        }
      });
    } catch (error) {
      console.error('Error getting farms:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Failed to get farms'
        }
      });
    }
  };

  /**
   * Get farm by ID
   */
  getFarmById = async (req: Request, res: Response): Promise<void> => {
    try {
      const { farmId } = req.params;

      const farm = await this.farmRepository.findById(farmId);

      if (!farm) {
        res.status(404).json({
          success: false,
          error: {
            code: 'FARM_NOT_FOUND',
            message: 'Farm not found'
          }
        });
        return;
      }

      res.status(200).json({
        success: true,
        data: farm
      });
    } catch (error) {
      console.error('Error getting farm:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Failed to get farm'
        }
      });
    }
  };

  /**
   * Update farm details
   */
  updateFarm = async (req: Request, res: Response): Promise<void> => {
    try {
      const { farmId } = req.params;
      const updateData = req.body;

      const farm = await this.farmRepository.findById(farmId);

      if (!farm) {
        res.status(404).json({
          success: false,
          error: {
            code: 'FARM_NOT_FOUND',
            message: 'Farm not found'
          }
        });
        return;
      }

      // Remove fields that shouldn't be updated directly
      delete updateData.farmId;
      delete updateData.createdAt;

      const updatedFarm = await this.farmRepository.update(farmId, updateData);

      res.status(200).json({
        success: true,
        data: {
          message: 'Farm updated successfully',
          farm: updatedFarm
        }
      });
    } catch (error) {
      console.error('Error updating farm:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Failed to update farm'
        }
      });
    }
  };

  /**
   * Verify farm
   */
  verifyFarm = async (req: Request, res: Response): Promise<void> => {
    try {
      const { farmId } = req.params;
      const { approved, notes, adminId } = req.body;

      const farm = await this.farmRepository.findById(farmId);

      if (!farm) {
        res.status(404).json({
          success: false,
          error: {
            code: 'FARM_NOT_FOUND',
            message: 'Farm not found'
          }
        });
        return;
      }

      // Since farm schema doesn't have verification fields, we'll add them to certifications
      // and update the status based on approval
      const currentCertifications = farm.certifications || [];
      let verificationCert = `${approved ? 'VERIFIED' : 'REJECTED'}_BY_ADMIN_${adminId || 'ADMIN'}_${new Date().toISOString()}`;
      if (notes) {
        verificationCert += `_NOTES_${notes.replace(/\s+/g, '_')}`;
      }

      const verificationData = {
        certifications: [...currentCertifications, verificationCert],
        status: (approved ? 'ACTIVE' : 'INACTIVE') as 'ACTIVE' | 'INACTIVE'
      };

      const updatedFarm = await this.farmRepository.update(farmId, verificationData);

      res.status(200).json({
        success: true,
        data: {
          message: `Farm ${approved ? 'verified' : 'rejected'} successfully`,
          farm: updatedFarm,
          timestamp: new Date().toISOString()
        }
      });
    } catch (error) {
      console.error('Error verifying farm:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Failed to verify farm'
        }
      });
    }
  };

  /**
   * Onboard a new seller (admin)
   */
  onboardSeller = async (req: Request, res: Response): Promise<void> => {
    try {
      const registerDto: RegisterSellerDto = req.body;
      // Validate required fields
      if (!registerDto.name || !registerDto.email || !registerDto.contact || !registerDto.address || !registerDto.password) {
        res.status(400).json({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Missing required fields for seller onboarding.'
          }
        });
        return;
      }
      const mongoConnector = dbManager.getMongoDBConnector();
      if (!mongoConnector) {
        res.status(500).json({
          success: false,
          error: {
            code: 'DB_NOT_CONNECTED',
            message: 'MongoDB connector not initialized.'
          }
        });
        return;
      }
      const onboardingService = new SellerOnboardingService(mongoConnector);
      const seller = await onboardingService.registerSeller(registerDto);
      
      // If additional details are provided, update the seller with those details
      if (registerDto.documents || registerDto.bankDetails || registerDto.farmingInfo) {
        const additionalDetails: any = {};
        
        if (registerDto.documents) {
          additionalDetails.documents = registerDto.documents;
        }
        
        if (registerDto.bankDetails) {
          additionalDetails.bankDetails = registerDto.bankDetails;
        }
        
        if (registerDto.farmingInfo) {
          additionalDetails.farmingInfo = registerDto.farmingInfo;
        }
        
        // Update the seller with additional details
        await this.sellerRepository.update(seller.sellerId, additionalDetails);
        
        // Fetch the updated seller
        const updatedSeller = await this.sellerRepository.findById(seller.sellerId);
        res.status(201).json({
          success: true,
          data: updatedSeller
        });
        return;
      }
      
      res.status(201).json({
        success: true,
        data: seller
      });
    } catch (error: any) {
      console.error('Error onboarding seller:', error);
      if (error.message === 'Email already registered') {
        res.status(409).json({
          success: false,
          error: {
            code: 'EMAIL_EXISTS',
            message: error.message
          }
        });
      } else {
        res.status(500).json({
          success: false,
          error: {
            code: 'INTERNAL_ERROR',
            message: 'Failed to onboard seller.'
          }
        });
      }
    }
  };
} 
