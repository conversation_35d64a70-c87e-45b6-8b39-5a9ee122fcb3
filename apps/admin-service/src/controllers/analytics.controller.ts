import { Request, Response } from 'express';
import { dbManager } from '../../../../libs/shared/database-connectors/db-manager';
import { SellerRepository } from '../../../../libs/shared/database-connectors/repositories/mongo/seller.repository';
import { FarmRepository } from '../../../../libs/shared/database-connectors/repositories/mongo/farm.repository';
import { AdminModel } from '../../../../libs/shared/database-connectors/schemas/mongo/admin.schema';
import { CropModel } from '../../../../libs/shared/database-connectors/schemas/mongo/crop.schema';

export class AnalyticsController {
  private sellerRepository: SellerRepository;
  private farmRepository: FarmRepository;

  constructor() {
    const mongoConnector = dbManager.getMongoDBConnector();
    this.sellerRepository = new SellerRepository(mongoConnector);
    this.farmRepository = new FarmRepository(mongoConnector);
  }

  /**
   * Get dashboard statistics
   */
  getDashboardStats = async (req: Request, res: Response): Promise<void> => {
    try {
      // Get real statistics from database
      const [
        totalAdmins,
        totalSellers,
        totalFarms,
        totalCrops,
        sellersByStatus,
        farmsByStatus,
        cropsByGrowthStage,
        recentSellers
      ] = await Promise.all([
        AdminModel.countDocuments(),
        this.sellerRepository.findAll(1, 1).then(result => result.total),
        this.farmRepository.findAll(1, 1).then(result => result.total),
        CropModel.countDocuments(),
        this.getSellerStatusBreakdown(),
        this.getFarmStatusBreakdown(),
        this.getCropGrowthStageBreakdown(),
        this.getRecentSellers()
      ]);

      const stats = {
        overview: {
          totalAdmins,
          totalSellers,
          totalFarms,
          totalCrops,
          totalUsers: totalAdmins + totalSellers
        },
        breakdowns: {
          sellersByStatus,
          farmsByStatus,
          cropsByGrowthStage
        },
        recentActivity: [
          ...recentSellers.map(seller => ({
            type: 'seller_registration',
            message: `New seller registered: ${seller.personalInfo.name}`,
            timestamp: seller.createdAt.toISOString(),
            entityId: seller.sellerId
          }))
        ],
        systemHealth: {
          status: 'healthy',
          uptime: '99.9%',
          lastUpdate: new Date().toISOString(),
          services: {
            database: 'connected',
            elasticsearch: 'connected'
          }
        }
      };

      res.status(200).json({
        success: true,
        data: stats
      });
    } catch (error) {
      console.error('Error getting dashboard stats:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Failed to get dashboard statistics'
        }
      });
    }
  };

  /**
   * Get user analytics
   */
  getUserAnalytics = async (req: Request, res: Response): Promise<void> => {
    try {
      const { period = '30d' } = req.query;

      // Calculate date range based on period
      const endDate = new Date();
      const startDate = new Date();

      switch (period) {
        case '7d':
          startDate.setDate(endDate.getDate() - 7);
          break;
        case '30d':
          startDate.setDate(endDate.getDate() - 30);
          break;
        case '90d':
          startDate.setDate(endDate.getDate() - 90);
          break;
        default:
          startDate.setDate(endDate.getDate() - 30);
      }

      // Get real analytics data
      const [
        totalUsers,
        newSellers,
        sellersByLocation,
        sellersByVerificationStatus
      ] = await Promise.all([
        this.getTotalUsersCount(),
        this.getNewSellersInPeriod(startDate, endDate),
        this.getSellersByLocation(),
        this.getSellersByVerificationStatus()
      ]);

      const analytics = {
        period,
        totalUsers,
        newUsers: newSellers.length,
        demographics: {
          byLocation: sellersByLocation,
          byVerificationStatus: sellersByVerificationStatus
        },
        registrationTrends: await this.getRegistrationTrends(startDate, endDate)
      };

      res.status(200).json({
        success: true,
        data: analytics
      });
    } catch (error) {
      console.error('Error getting user analytics:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Failed to get user analytics'
        }
      });
    }
  };

  /**
   * Get sales analytics
   */
  getSalesAnalytics = async (req: Request, res: Response): Promise<void> => {
    try {
      const { period = '30d' } = req.query;
      
      // Mock sales analytics data
      const analytics = {
        period,
        totalSales: 185000,
        totalOrders: 2750,
        averageOrderValue: 67.27,
        topProducts: [
          { name: 'Organic Tomatoes', sales: 25000, orders: 350 },
          { name: 'Fresh Wheat', sales: 22000, orders: 280 },
          { name: 'Organic Rice', sales: 18000, orders: 220 }
        ],
        salesTrends: [
          { date: '2024-01-01', sales: 4500, orders: 65 },
          { date: '2024-01-02', sales: 5200, orders: 78 },
          { date: '2024-01-03', sales: 3800, orders: 52 }
        ],
        topSellers: [
          { name: 'Green Valley Farm', sales: 15000, orders: 85 },
          { name: 'Sunrise Agriculture', sales: 12000, orders: 72 },
          { name: 'Golden Harvest Co.', sales: 9500, orders: 58 }
        ]
      };
      
      res.status(200).json({
        success: true,
        data: analytics
      });
    } catch (error) {
      console.error('Error getting sales analytics:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Failed to get sales analytics'
        }
      });
    }
  };

  /**
   * Get crop analytics
   */
  getCropAnalytics = async (req: Request, res: Response): Promise<void> => {
    try {
      const { period = '30d' } = req.query;
      
      // Mock crop analytics data
      const analytics = {
        period,
        totalCrops: 1580,
        activeCrops: 920,
        harvestedCrops: 450,
        cropsByType: [
          { type: 'Vegetables', count: 650, percentage: 41 },
          { type: 'Grains', count: 520, percentage: 33 },
          { type: 'Fruits', count: 280, percentage: 18 },
          { type: 'Herbs', count: 130, percentage: 8 }
        ],
        cropHealth: {
          healthy: 780,
          warning: 120,
          critical: 20
        },
        yieldPrediction: {
          expectedYield: '2500 tons',
          confidence: '85%',
          marketValue: '₹15,00,000'
        },
        seasonalTrends: [
          { month: 'Jan', planted: 120, harvested: 85 },
          { month: 'Feb', planted: 150, harvested: 95 },
          { month: 'Mar', planted: 180, harvested: 110 }
        ]
      };
      
      res.status(200).json({
        success: true,
        data: analytics
      });
    } catch (error) {
      console.error('Error getting crop analytics:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Failed to get crop analytics'
        }
      });
    }
  };

  /**
   * Export users data
   */
  exportUsers = async (req: Request, res: Response): Promise<void> => {
    try {
      const { format = 'csv' } = req.query;
      
      // Mock export data
      const exportData = {
        exportId: `export_users_${Date.now()}`,
        format,
        status: 'initiated',
        message: 'User data export initiated successfully',
        estimatedTime: '2-3 minutes',
        downloadUrl: `/api/v1/admin/downloads/export_users_${Date.now()}.${format}`,
        timestamp: new Date().toISOString()
      };
      
      res.status(200).json({
        success: true,
        data: exportData
      });
    } catch (error) {
      console.error('Error exporting users:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'EXPORT_ERROR',
          message: 'Failed to export user data'
        }
      });
    }
  };

  /**
   * Export sellers data
   */
  exportSellers = async (req: Request, res: Response): Promise<void> => {
    try {
      const { format = 'csv', status } = req.query;
      
      // Mock export data
      const exportData = {
        exportId: `export_sellers_${Date.now()}`,
        format,
        filters: status ? { status } : {},
        status: 'initiated',
        message: 'Seller data export initiated successfully',
        estimatedTime: '2-3 minutes',
        downloadUrl: `/api/v1/admin/downloads/export_sellers_${Date.now()}.${format}`,
        timestamp: new Date().toISOString()
      };
      
      res.status(200).json({
        success: true,
        data: exportData
      });
    } catch (error) {
      console.error('Error exporting sellers:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'EXPORT_ERROR',
          message: 'Failed to export seller data'
        }
      });
    }
  };

  /**
   * Export analytics data
   */
  exportAnalytics = async (req: Request, res: Response): Promise<void> => {
    try {
      const { format = 'xlsx', type = 'dashboard', period = '30d' } = req.query;
      
      // Mock export data
      const exportData = {
        exportId: `export_analytics_${Date.now()}`,
        format,
        type,
        period,
        status: 'initiated',
        message: 'Analytics data export initiated successfully',
        estimatedTime: '3-5 minutes',
        downloadUrl: `/api/v1/admin/downloads/export_analytics_${Date.now()}.${format}`,
        timestamp: new Date().toISOString()
      };
      
      res.status(200).json({
        success: true,
        data: exportData
      });
    } catch (error) {
      console.error('Error exporting analytics:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'EXPORT_ERROR',
          message: 'Failed to export analytics data'
        }
      });
    }
  };

  // Helper methods for analytics
  private async getSellerStatusBreakdown(): Promise<any> {
    try {
      const { sellers } = await this.sellerRepository.findAll(1, 1000);
      const breakdown = sellers.reduce((acc: any, seller) => {
        acc[seller.status] = (acc[seller.status] || 0) + 1;
        return acc;
      }, {});
      return breakdown;
    } catch (error) {
      console.error('Error getting seller status breakdown:', error);
      return {};
    }
  }

  private async getFarmStatusBreakdown(): Promise<any> {
    try {
      const { farms } = await this.farmRepository.findAll(1, 1000);
      const breakdown = farms.reduce((acc: any, farm) => {
        acc[farm.status] = (acc[farm.status] || 0) + 1;
        return acc;
      }, {});
      return breakdown;
    } catch (error) {
      console.error('Error getting farm status breakdown:', error);
      return {};
    }
  }

  private async getCropGrowthStageBreakdown(): Promise<any> {
    try {
      const crops = await CropModel.find().select('growthStage');
      const breakdown = crops.reduce((acc: any, crop) => {
        acc[crop.growthStage] = (acc[crop.growthStage] || 0) + 1;
        return acc;
      }, {});
      return breakdown;
    } catch (error) {
      console.error('Error getting crop growth stage breakdown:', error);
      return {};
    }
  }

  private async getRecentSellers(): Promise<any[]> {
    try {
      const { sellers } = await this.sellerRepository.findAll(1, 5);
      return sellers.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
    } catch (error) {
      console.error('Error getting recent sellers:', error);
      return [];
    }
  }

  private async getTotalUsersCount(): Promise<number> {
    try {
      const [adminCount, sellerCount] = await Promise.all([
        AdminModel.countDocuments(),
        this.sellerRepository.findAll(1, 1).then(result => result.total)
      ]);
      return adminCount + sellerCount;
    } catch (error) {
      console.error('Error getting total users count:', error);
      return 0;
    }
  }

  private async getNewSellersInPeriod(startDate: Date, endDate: Date): Promise<any[]> {
    try {
      const { sellers } = await this.sellerRepository.findAll(1, 1000);
      return sellers.filter(seller =>
        seller.createdAt >= startDate && seller.createdAt <= endDate
      );
    } catch (error) {
      console.error('Error getting new sellers in period:', error);
      return [];
    }
  }

  private async getSellersByLocation(): Promise<any[]> {
    try {
      const { sellers } = await this.sellerRepository.findAll(1, 1000);
      const locationMap = sellers.reduce((acc: any, seller) => {
        const state = seller.personalInfo.address.state;
        acc[state] = (acc[state] || 0) + 1;
        return acc;
      }, {});

      return Object.entries(locationMap).map(([state, count]) => ({
        state,
        count
      }));
    } catch (error) {
      console.error('Error getting sellers by location:', error);
      return [];
    }
  }

  private async getSellersByVerificationStatus(): Promise<any[]> {
    try {
      const { sellers } = await this.sellerRepository.findAll(1, 1000);
      const statusMap = sellers.reduce((acc: any, seller) => {
        acc[seller.verificationStatus] = (acc[seller.verificationStatus] || 0) + 1;
        return acc;
      }, {});

      return Object.entries(statusMap).map(([status, count]) => ({
        status,
        count
      }));
    } catch (error) {
      console.error('Error getting sellers by verification status:', error);
      return [];
    }
  }

  private async getRegistrationTrends(startDate: Date, endDate: Date): Promise<any[]> {
    try {
      const { sellers } = await this.sellerRepository.findAll(1, 1000);
      const filteredSellers = sellers.filter(seller =>
        seller.createdAt >= startDate && seller.createdAt <= endDate
      );

      // Group by date
      const trends = filteredSellers.reduce((acc: any, seller) => {
        const date = seller.createdAt.toISOString().split('T')[0];
        acc[date] = (acc[date] || 0) + 1;
        return acc;
      }, {});

      return Object.entries(trends).map(([date, registrations]) => ({
        date,
        registrations
      }));
    } catch (error) {
      console.error('Error getting registration trends:', error);
      return [];
    }
  }
}