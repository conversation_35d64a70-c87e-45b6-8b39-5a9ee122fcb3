import { Router } from 'express';
import { SystemController } from '../controllers/system.controller';
import { UserManagementController } from '../controllers/user-management.controller';
import { AnalyticsController } from '../controllers/analytics.controller';
import { AuthController } from '../controllers/auth.controller';
import { CropManagementController } from '../controllers/crop-management.controller';
import { standardLimiter, authLimiter, exportLimiter } from '../middleware/rate-limiter.middleware';

const router = Router();
const systemController = new SystemController();
const userManagementController = new UserManagementController();
const analyticsController = new AnalyticsController();
const authController = new AuthController();
const cropManagementController = new CropManagementController();

// System Management Routes
router.get('/system/health', standardLimiter, systemController.getSystemHealth);
router.get('/system/stats', standardLimiter, systemController.getSystemStats);
router.get('/system/seed', standardLimiter, systemController.seedDatabase);
router.post('/system/backup', standardLimiter, systemController.createBackup);
router.get('/system/logs', standardLimiter, systemController.getSystemLogs);

// User Management Routes
router.get('/users', standardLimiter, userManagementController.getAllUsers);
router.get('/users/:userId', standardLimiter, userManagementController.getUserById);
router.put('/users/:userId/status', standardLimiter, userManagementController.updateUserStatus);
router.delete('/users/:userId', standardLimiter, userManagementController.deleteUser);

// Seller Management Routes
router.get('/sellers', standardLimiter, userManagementController.getAllSellers);
router.get('/sellers/:sellerId', standardLimiter, userManagementController.getSellerById);
router.put('/sellers/:sellerId', standardLimiter, userManagementController.updateSeller);
router.put('/sellers/:sellerId/verify', standardLimiter, userManagementController.verifySeller);
router.put('/sellers/:sellerId/status', standardLimiter, userManagementController.updateSellerStatus);
router.post('/sellers', standardLimiter, userManagementController.onboardSeller);

// Farm Management Routes
router.get('/farms', standardLimiter, userManagementController.getAllFarms);
router.get('/farms/:farmId', standardLimiter, userManagementController.getFarmById);
router.put('/farms/:farmId', standardLimiter, userManagementController.updateFarm);
router.put('/farms/:farmId/verify', standardLimiter, userManagementController.verifyFarm);

// Crop Management Routes
router.get('/crops', standardLimiter, cropManagementController.getAllCrops);
router.get('/crops/:cropId', standardLimiter, cropManagementController.getCropById);
router.put('/crops/:cropId', standardLimiter, cropManagementController.updateCrop);
router.put('/crops/:cropId/verify', standardLimiter, cropManagementController.verifyCrop);
router.delete('/crops/:cropId', standardLimiter, cropManagementController.deleteCrop);

// Analytics Routes
router.get('/analytics/dashboard', standardLimiter, analyticsController.getDashboardStats);
router.get('/analytics/users', standardLimiter, analyticsController.getUserAnalytics);
router.get('/analytics/sales', standardLimiter, analyticsController.getSalesAnalytics);
router.get('/analytics/crops', standardLimiter, analyticsController.getCropAnalytics);

// Data Export Routes
router.get('/export/users', exportLimiter, analyticsController.exportUsers);
router.get('/export/sellers', exportLimiter, analyticsController.exportSellers);
router.get('/export/analytics', exportLimiter, analyticsController.exportAnalytics);

// Admin Auth Route
router.post('/login', authLimiter, authController.login);

export default router; 