/**
 * Validation schemas for seller DTOs
 */

// Email regex pattern
const EMAIL_PATTERN = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;

// Indian phone number regex (10 digits)
const PHONE_PATTERN = /^[6-9]\d{9}$/;

// Indian PIN code regex (6 digits)
const PINCODE_PATTERN = /^\d{6}$/;

// IFSC code regex
const IFSC_PATTERN = /^[A-Z]{4}0[A-Z0-9]{6}$/;

/**
 * Schema for seller registration validation
 */
export const registerSellerSchema = {
  name: {
    required: true,
    type: 'string' as const,
    minLength: 3,
    maxLength: 100,
  },
  email: {
    required: true,
    type: 'string' as const,
    pattern: EMAIL_PATTERN,
  },
  contact: {
    required: true,
    type: 'string' as const,
    pattern: PHONE_PATTERN,
  },
  address: {
    required: true,
    type: 'object' as const,
    validate: (value: any) => {
      if (!value.state || !value.city || !value.pincode || !value.addressLine1) {
        return {
          valid: false,
          message: 'Address must include state, city, pincode, and addressLine1',
        };
      }
      return true;
    },
  },
  password: {
    required: true,
    type: 'string' as const,
    minLength: 8,
    maxLength: 100,
  },
};

/**
 * Schema for seller documents validation
 */
export const sellerDocumentsSchema = {
  identityProof: {
    required: true,
    type: 'string' as const,
  },
  landOwnership: {
    required: true,
    type: 'string' as const,
  },
  certifications: {
    type: 'array' as const,
  },
};

/**
 * Schema for seller verification status update validation
 */
export const verificationStatusSchema = {
  sellerId: {
    required: true,
    type: 'string' as const,
  },
  status: {
    required: true,
    type: 'string' as const,
    validate: (value: string) => {
      return ['PENDING', 'VERIFIED', 'REJECTED'].includes(value);
    },
  },
  reason: {
    type: 'string' as const,
  },
};

/**
 * Schema for seller profile update validation
 */
export const updateProfileSchema = {
  name: {
    type: 'string' as const,
    minLength: 3,
    maxLength: 100,
  },
  contact: {
    type: 'string' as const,
    pattern: PHONE_PATTERN,
  },
  email: {
    type: 'string' as const,
    pattern: EMAIL_PATTERN,
  },
  address: {
    type: 'object' as const,
  },
};

/**
 * Schema for seller bank details validation
 */
export const bankDetailsSchema = {
  accountNumber: {
    required: true,
    type: 'string' as const,
    minLength: 9,
    maxLength: 18,
  },
  bankName: {
    required: true,
    type: 'string' as const,
    minLength: 3,
    maxLength: 100,
  },
  ifscCode: {
    required: true,
    type: 'string' as const,
    pattern: IFSC_PATTERN,
  },
};

/**
 * Schema for seller login validation
 */
export const loginSchema = {
  email: {
    required: true,
    type: 'string' as const,
    pattern: EMAIL_PATTERN,
  },
  password: {
    required: true,
    type: 'string' as const,
  },
};

/**
 * Schema for seller ID validation
 */
export const sellerIdParamSchema = {
  sellerId: {
    required: true,
    type: 'string' as const,
    minLength: 5,
  },
}; 