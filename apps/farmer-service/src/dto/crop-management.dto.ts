/**
 * DTOs for crop management operations in seller service
 */

export interface CreateCropDto {
  numberOfPlots: number;
  farmId: string;
  name: string;
  type: string;
  variety: string;
  plantingDate: Date;
  expectedHarvestDate: Date;
  season: 'KHARIF' | 'RABI' | 'ZAID';
  resources?: {
    water: number;
    fertilizer: number;
    pesticides: number;
  };
  soilConditions?: {
    type: string;
    ph: number;
    nutrients: string[];
  };
  waterAvailability?: string;
  metadata?: {
    cropCategory?: string;
    farmingMethod?: 'ORGANIC' | 'CONVENTIONAL' | 'INTEGRATED';
    irrigationMethod?: string;
    harvestSeason?: string;
    pestDiseaseStatus?: string;
    storageMethod?: string;
    nutrientManagement?: string;
    waterSource?: string;
    pesticideUsage?: string;
    seedType?: string;
    harvestingMethod?: string;
  };
}

export interface UpdateCropDto {
  name?: string;
  type?: string;
  variety?: string;
  plantingDate?: Date;
  expectedHarvestDate?: Date;
  actualHarvestDate?: Date;
  growthStage?: 'PLANTING' | 'GROWING' | 'FLOWERING' | 'MATURING' | 'READY' | 'HARVESTED';
  healthStatus?: 'HEALTHY' | 'WARNING' | 'CRITICAL';
  healthIssues?: string[];
  yield?: {
    expected?: number;
    actual?: number;
    unit?: string;
  };
  resources?: {
    water?: number;
    fertilizer?: number;
    pesticides?: number;
  };
  soilConditions?: {
    type?: string;
    ph?: number;
    nutrients?: string[];
  };
  waterAvailability?: string;
  metadata?: {
    cropCategory?: string;
    farmingMethod?: 'ORGANIC' | 'CONVENTIONAL' | 'INTEGRATED';
    irrigationMethod?: string;
    harvestSeason?: string;
    pestDiseaseStatus?: string;
    storageMethod?: string;
    nutrientManagement?: string;
    waterSource?: string;
    pesticideUsage?: string;
    seedType?: string;
    harvestingMethod?: string;
  };
}

export interface CropResponseDto {
  cropId: string;
  plotId: string;
  farmId: string;
  sellerId: string;
  name: string;
  type: string;
  variety: string;
  plantingDate: Date;
  expectedHarvestDate: Date;
  actualHarvestDate?: Date;
  growthStage: 'PLANTING' | 'GROWING' | 'FLOWERING' | 'MATURING' | 'READY' | 'HARVESTED';
  health: {
    status: 'HEALTHY' | 'WARNING' | 'CRITICAL';
    issues: string[];
    lastCheck: Date;
  };
  yield: {
    expected: number;
    actual: number;
    unit: string;
  };
  resources: {
    water: number;
    fertilizer: number;
    pesticides: number;
  };
  soilConditions: {
    type: string;
    ph: number;
    nutrients: string[];
  };
  waterAvailability: string;
  metadata: {
    cropCategory: string;
    farmingMethod: 'ORGANIC' | 'CONVENTIONAL' | 'INTEGRATED';
    irrigationMethod: string;
    harvestSeason: string;
    pestDiseaseStatus: string;
    storageMethod: string;
    nutrientManagement: string;
    waterSource: string;
    pesticideUsage: string;
    seedType: string;
    harvestingMethod: string;
  };
  createdAt: Date;
  updatedAt: Date;
}

export interface CropAnalyticsDto {
  cropId: string;
  name: string;
  type: string;
  variety: string;
  daysToHarvest: number;
  growthProgress: number; // percentage
  healthScore: number; // 0-100
  yieldProjection: {
    expected: number;
    current: number;
    unit: string;
    confidence: number; // percentage
  };
  resourceUtilization: {
    water: { used: number; planned: number; efficiency: number };
    fertilizer: { used: number; planned: number; efficiency: number };
    pesticides: { used: number; planned: number; efficiency: number };
  };
  sustainabilityMetrics: {
    waterUsage: 'LOW' | 'MODERATE' | 'HIGH';
    carbonFootprint: 'LOW' | 'MEDIUM' | 'HIGH';
    pesticideUsage: 'ORGANIC' | 'LOW' | 'MODERATE' | 'HIGH';
    overallScore: number;
  };
  recentActivities: {
    date: Date;
    activity: string;
    details: string;
    performedBy: string;
  }[];
}

export interface CropSearchDto {
  sellerId?: string;
  farmId?: string;
  plotId?: string;
  name?: string;
  type?: string;
  variety?: string;
  growthStage?: 'PLANTING' | 'GROWING' | 'FLOWERING' | 'MATURING' | 'READY' | 'HARVESTED';
  healthStatus?: 'HEALTHY' | 'WARNING' | 'CRITICAL';
  season?: 'KHARIF' | 'RABI' | 'ZAID';
  farmingMethod?: 'ORGANIC' | 'CONVENTIONAL' | 'INTEGRATED';
  harvestDateFrom?: Date;
  harvestDateTo?: Date;
  page?: number;
  limit?: number;
  sortBy?: 'name' | 'plantingDate' | 'expectedHarvestDate' | 'growthStage';
  sortOrder?: 'asc' | 'desc';
}

export interface CropListResponseDto {
  crops: CropResponseDto[];
  total: number;
  page: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

export interface CropMaintenanceDto {
  cropId: string;
  activity: 'IRRIGATION' | 'FERTILIZATION' | 'PEST_CONTROL' | 'INSPECTION' | 'HARVESTING';
  scheduledDate: Date;
  completedDate?: Date;
  notes?: string;
  resources?: {
    water?: number;
    fertilizer?: number;
    pesticides?: number;
  };
  performedBy?: string;
  status: 'SCHEDULED' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELLED';
}

export interface UpdateCropHealthDto {
  healthStatus: 'HEALTHY' | 'WARNING' | 'CRITICAL';
  issues?: string[];
  notes?: string;
  inspectionDate: Date;
  inspectedBy: string;
  recommendations?: string[];
}
