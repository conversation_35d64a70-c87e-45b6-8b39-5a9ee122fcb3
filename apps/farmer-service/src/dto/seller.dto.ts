/**
 * Data Transfer Objects for Seller Service
 */

/**
 * DTO for seller registration
 */
export interface RegisterSellerDto {
  name: string;
  email: string;
  contact: string;
  address: {
    country?: string;
    state: string;
    city: string;
    pincode: string;
    addressLine1: string;
    addressLine2?: string;
  };
  password: string;
  // Optional fields
  documents?: {
    identityProof?: string;
    landOwnership?: string;
    certifications?: string[];
  };
  bankDetails?: {
    accountNumber: string;
    bankName: string;
    ifscCode: string;
  };
  farmingInfo?: {
    experience?: number;
    specializations?: string[];
    certifications?: {
      certId: string;
      type: string;
      issuer: string;
      issueDate: Date;
      expiryDate: Date;
    }[];
    training?: {
      trainingId: string;
      name: string;
      completionDate: Date;
      status: 'COMPLETED' | 'IN_PROGRESS' | 'PENDING';
    }[];
  };
}

/**
 * DTO for seller verification documents
 */
export interface SellerDocumentsDto {
  identityProof: string; // Document ID or file path
  landOwnership: string; // Document ID or file path
  certifications?: string[]; // Array of certification document IDs or file paths
}

/**
 * DTO for seller bank details
 */
export interface SellerBankDetailsDto {
  accountNumber: string;
  bankName: string;
  ifscCode: string;
}

/**
 * DTO for seller profile update
 */
export interface UpdateSellerProfileDto {
  name?: string;
  contact?: string;
  email?: string;
  address?: {
    country?: string;
    state?: string;
    city?: string;
    pincode?: string;
    addressLine1?: string;
    addressLine2?: string;
  };
}

/**
 * DTO for seller verification status update
 */
export interface UpdateVerificationStatusDto {
  sellerId: string;
  status: 'PENDING' | 'VERIFIED' | 'REJECTED';
  reason?: string;
}

/**
 * DTO for seller login
 */
export interface SellerLoginDto {
  email: string;
  password: string;
} 
