/**
 * DTO for a single KYC document.
 *
 * Step 1: Upload the document file using the /documents/:sellerId endpoint (multipart/form-data, field: 'document').
 *         The response will include a fileUrl (e.g., /assets/kyc/filename.pdf).
 * Step 2: Use the returned fileUrl as the documentUrl in this DTO when submitting KYC.
 */
export interface KYCDocumentDto {
  documentType: 'aadhaar' | 'pan' | 'driving_license' | 'passport' | 'bank_statement' | 'address_proof';
  documentNumber: string;
  documentUrl: string; // Use the fileUrl returned from the upload endpoint
  isVerified?: boolean;
  verificationDate?: Date;
  rejectionReason?: string;
}

/**
 * DTO for submitting KYC information.
 *
 * The documents array should contain file URLs obtained from the document upload endpoint.
 */
export interface KYCSubmissionDto {
  sellerId: string;
  documents: KYCDocumentDto[]; // Use file URLs from upload step
  personalDetails: {
    fullName: string;
    dateOfBirth: Date;
    address: {
      street: string;
      city: string;
      state: string;
      zipCode: string;
      country: string;
    };
    bankDetails: {
      accountNumber: string;
      ifscCode: string;
      accountHolderName: string;
      bankName: string;
    };
  };
}

export interface KYCVerificationDto {
  kycId: string;
  sellerId: string;
  status: 'pending' | 'in_review' | 'verified' | 'rejected';
  documents: KYCDocumentDto[];
  verificationNotes?: string;
  verifiedBy?: string;
  verificationDate?: Date;
}

export interface DocumentUploadDto {
  file: any; // Will be typed properly when multer is configured
  documentType: string;
  sellerId: string;
}

export interface SellerStatusDto {
  sellerId: string;
  status: 'active' | 'inactive' | 'suspended' | 'pending_verification';
  reason?: string;
  updatedBy: string;
}

export interface SellerDashboardDto {
  sellerId: string;
  basicInfo: {
    name: string;
    email: string;
    phone: string;
    status: string;
    kycStatus: string;
    joinDate: Date;
  };
  performance: {
    totalOrders: number;
    totalRevenue: number;
    averageRating: number;
    totalReviews: number;
  };
  recentActivity: {
    type: string;
    description: string;
    timestamp: Date;
  }[];
} 