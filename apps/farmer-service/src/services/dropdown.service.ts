import { MongoDBConnector, FarmRepository, EnumService, EnumCategoryType, CropModel, ICrop } from '@libs/shared';
import {
  FarmDropdownDto,
  CropDropdownDto,
  EnumDropdownDto,
  DropdownQueryDto
} from '../dto/dropdown.dto';

/**
 * Service for handling dropdown listing operations
 */
export class DropdownService {
  private farmRepository: FarmRepository;

  constructor(dbConnector: MongoDBConnector) {
    this.farmRepository = new FarmRepository(dbConnector);
  }

  /**
   * Get farms dropdown list for a seller
   * @param sellerId - ID of the seller
   * @param queryDto - Query parameters
   * @returns Array of farm dropdown items
   */
  async getFarmsDropdown(sellerId: string, queryDto: DropdownQueryDto = {}): Promise<{ farms: FarmDropdownDto[]; total: number }> {
    try {
      const { status, search, limit = 50 } = queryDto;

      // Get all farms for the seller first
      let farms = await this.farmRepository.findBySellerId(sellerId);

      // Apply status filter if provided
      if (status) {
        farms = farms.filter(farm => farm.status === status);
      }

      // Apply search filter if provided
      if (search) {
        const searchLower = search.toLowerCase();
        farms = farms.filter(farm =>
          farm.name.toLowerCase().includes(searchLower) ||
          farm.location?.city?.toLowerCase().includes(searchLower) ||
          farm.location?.state?.toLowerCase().includes(searchLower)
        );
      }

      // Apply limit
      const total = farms.length;
      const limitedFarms = farms.slice(0, limit);

      // Transform to dropdown format
      const farmDropdowns: FarmDropdownDto[] = limitedFarms.map(farm => ({
        farmId: farm.farmId,
        name: farm.name,
        status: farm.status as 'ACTIVE' | 'INACTIVE',
        location: {
          city: farm.location?.city || '',
          state: farm.location?.state || ''
        }
      }));

      return { farms: farmDropdowns, total };
    } catch (error) {
      console.error('Error getting farms dropdown:', error);
      throw error;
    }
  }

  /**
   * Get crops dropdown list for a seller
   * @param sellerId - ID of the seller
   * @param queryDto - Query parameters
   * @returns Array of crop dropdown items
   */
  async getCropsDropdown(sellerId: string, queryDto: DropdownQueryDto = {}): Promise<{ crops: CropDropdownDto[]; total: number }> {
    try {
      const { status, search, limit = 50 } = queryDto;

      // Build query filter
      const filter: any = { sellerId };

      if (status) {
        filter['health.status'] = status; // Crop status is in health.status field
      }

      if (search) {
        filter.$or = [
          { name: { $regex: search, $options: 'i' } },
          { type: { $regex: search, $options: 'i' } }
        ];
      }

      // Get crops with minimal fields for dropdown using CropModel directly
      const crops = await CropModel.find(filter)
        .select('cropId name type health.status farmId')
        .limit(limit)
        .lean()
        .exec();

      const total = await CropModel.countDocuments(filter);

      // Transform to dropdown format
      const cropDropdowns: CropDropdownDto[] = crops.map((crop: any) => ({
        cropId: crop.cropId,
        name: crop.name,
        category: crop.type, // Using type as category
        status: crop.health?.status || 'UNKNOWN' as any,
        farmName: crop.farmId // Using farmId as farmName for now
      }));

      return { crops: cropDropdowns, total };
    } catch (error) {
      console.error('Error getting crops dropdown:', error);
      throw error;
    }
  }

  /**
   * Get enum values for dropdown
   * @param category - Enum category
   * @param subcategory - Enum subcategory
   * @returns Enum dropdown data
   */
  async getEnumDropdown(category: string, subcategory: string): Promise<EnumDropdownDto | null> {
    try {
      const enumCategory = category.toUpperCase() as EnumCategoryType;
      const enumValues = await EnumService.getEnumValues(enumCategory, subcategory);
      
      if (enumValues.length === 0) {
        return null;
      }

      return {
        name: `${category} - ${subcategory}`,
        values: enumValues
      };
    } catch (error) {
      console.error('Error getting enum dropdown:', error);
      throw error;
    }
  }

  /**
   * Get all available enum categories and subcategories
   * @returns Array of available enum categories
   */
  async getAvailableEnums(): Promise<{ category: string; subcategory: string; name: string }[]> {
    try {
      const enums = await EnumService.getAllEnums();
      
      return enums.map(enumDoc => ({
        category: enumDoc.category,
        subcategory: enumDoc.subcategory,
        name: enumDoc.name
      }));
    } catch (error) {
      console.error('Error getting available enums:', error);
      throw error;
    }
  }
}
