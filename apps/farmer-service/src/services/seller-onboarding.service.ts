import { RegisterSellerDto, SellerDocumentsDto, UpdateVerificationStatusDto } from '../dto/seller.dto';
import * as bcrypt from 'bcrypt';
import { v4 as uuidv4 } from 'uuid';
import { 
  SellerDocument, 
  SellerRepository,
  MongoDBConnector
} from '@libs/shared';

export class SellerOnboardingService {
  private sellerRepository: SellerRepository;
  
  constructor(dbConnector: MongoDBConnector) {
    this.sellerRepository = new SellerRepository(dbConnector);
  }
  
  /**
   * Register a new seller
   * @param registerDto Seller registration data
   * @returns Registered seller
   */
  async registerSeller(registerDto: RegisterSellerDto): Promise<SellerDocument> {
    try {
      // Check if email already exists
      const existingSeller = await this.sellerRepository.findByEmail(registerDto.email);
      if (existingSeller) {
        throw new Error('Email already registered');
      }
      
      // Hash password (in a real implementation, you'd store this in a user/auth service)
      const hashedPassword = await bcrypt.hash(registerDto.password, 10);
      
      // Generate seller ID
      const sellerId = `SELLER-${uuidv4().split('-')[0].toUpperCase()}`;
      
      // Create seller object with basic info
      const sellerData: any = {
        sellerId,
        personalInfo: {
          name: registerDto.name,
          email: registerDto.email,
          contact: registerDto.contact,
          address: {
            country: registerDto.address.country || 'INDIA',
            state: registerDto.address.state,
            city: registerDto.address.city,
            pincode: registerDto.address.pincode,
            addressLine1: registerDto.address.addressLine1,
            addressLine2: registerDto.address.addressLine2 || '',
          },
        },
        status: 'PENDING' as 'PENDING',
        verificationStatus: 'PENDING' as 'PENDING',
        statusHistory: [{
          status: 'PENDING',
          updatedBy: 'SYSTEM',
          reason: 'Initial registration',
          updatedAt: new Date()
        }]
      };
      
      // Add optional fields if provided
      if (registerDto.documents) {
        sellerData.documents = registerDto.documents;
      }
      
      if (registerDto.bankDetails) {
        sellerData.bankDetails = registerDto.bankDetails;
      }
      
      if (registerDto.farmingInfo) {
        sellerData.farmingInfo = registerDto.farmingInfo;
      }
      
      // Create seller
      return await this.sellerRepository.create(sellerData);
    } catch (error) {
      console.error('Error registering seller:', error);
      throw error;
    }
  }
  
  /**
   * Upload seller verification documents
   * @param sellerId Seller ID
   * @param documentsDto Document data
   * @returns Updated seller
   */
  async uploadDocuments(sellerId: string, documentsDto: SellerDocumentsDto): Promise<SellerDocument | null> {
    try {
      // Check if seller exists
      const seller = await this.sellerRepository.findById(sellerId);
      if (!seller) {
        throw new Error('Seller not found');
      }
      
      // Update documents
      return await this.sellerRepository.update(sellerId, {
        documents: {
          identityProof: documentsDto.identityProof,
          landOwnership: documentsDto.landOwnership,
          certifications: documentsDto.certifications || [],
        },
      });
    } catch (error) {
      console.error('Error uploading documents:', error);
      throw error;
    }
  }
  
  /**
   * Update seller verification status
   * @param statusDto Verification status data
   * @returns Updated seller
   */
  async updateVerificationStatus(statusDto: UpdateVerificationStatusDto): Promise<SellerDocument | null> {
    try {
      // Check if seller exists
      const seller = await this.sellerRepository.findById(statusDto.sellerId);
      if (!seller) {
        throw new Error('Seller not found');
      }
      
      // Update verification status
      return await this.sellerRepository.updateVerificationStatus(
        statusDto.sellerId,
        statusDto.status
      );
    } catch (error) {
      console.error('Error updating verification status:', error);
      throw error;
    }
  }
  
  /**
   * Activate seller account
   * @param sellerId Seller ID
   * @returns Activated seller
   */
  async activateAccount(sellerId: string): Promise<SellerDocument | null> {
    try {
      // Check if seller exists
      const seller = await this.sellerRepository.findById(sellerId);
      if (!seller) {
        throw new Error('Seller not found');
      }
      
      // Check if seller is verified
      if (seller.verificationStatus !== 'VERIFIED') {
        throw new Error('Seller must be verified before activation');
      }
      
      // Update status to active
      return await this.sellerRepository.update(sellerId, {
        status: 'ACTIVE',
      });
    } catch (error) {
      console.error('Error activating account:', error);
      throw error;
    }
  }
  
  /**
   * Perform background check on seller
   * @param sellerId Seller ID
   * @returns Background check result
   */
  async performBackgroundCheck(sellerId: string): Promise<{passed: boolean; details?: string}> {
    try {
      // Check if seller exists
      const seller = await this.sellerRepository.findById(sellerId);
      if (!seller) {
        throw new Error('Seller not found');
      }
      
      // In a real implementation, this would call an external service or API
      // For demo purposes, we'll simulate a successful background check
      
      // Simulate background check logic
      const passedCheck = Math.random() > 0.1; // 90% pass rate
      
      if (passedCheck) {
        return { passed: true };
      } else {
        return { 
          passed: false, 
          details: 'Failed background check due to insufficient documentation' 
        };
      }
    } catch (error) {
      console.error('Error performing background check:', error);
      throw error;
    }
  }
} 
