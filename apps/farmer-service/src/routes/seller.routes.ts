import { Router } from 'express';
import { SellerOnboardingController } from '../controllers/seller-onboarding.controller';
import { SellerProfileController } from '../controllers/seller-profile.controller';
import { AuthController } from '../controllers/auth.controller';
import { KYCController } from '../controllers/kyc.controller';
import { FarmManagementController } from '../controllers/farm-management.controller';
import { DropdownController } from '../controllers/dropdown.controller';
import { protect } from '../middleware/auth.middleware';
import { validateBody, validateParams } from '../middleware/validation.middleware';
import { standardLimiter, authLimiter, registrationLimiter } from '../middleware/rate-limiter.middleware';
import {
  registerSellerSchema,
  sellerDocumentsSchema,
  verificationStatusSchema,
  updateProfileSchema,
  bankDetailsSchema,
  loginSchema,
  sellerIdParamSchema,
} from '../dto/validation.schemas';
import multer from 'multer';
import * as path from 'path';

const router = Router();
const onboardingController = new SellerOnboardingController();
const profileController = new SellerProfileController();
const authController = new AuthController();
const kycController = new KYCController();
const farmManagementController = new FarmManagementController();
const dropdownController = new DropdownController();

// Multer storage configuration for KYC documents
const kycStorage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, path.join(__dirname, '../assets/kyc/'));
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1e9);
    cb(null, uniqueSuffix + '-' + file.originalname.replace(/\s+/g, '_'));
  },
});

const kycUpload = multer({
  storage: kycStorage,
  limits: { fileSize: 5 * 1024 * 1024 }, // 5MB limit
  fileFilter: (req, file, cb) => {
    // Accept images and PDFs only
    if (/\.(jpg|jpeg|png|pdf)$/i.test(file.originalname)) {
      cb(null, true);
    } else {
      cb(new Error('Only .jpg, .jpeg, .png, and .pdf files are allowed!'));
    }
  },
});

// Auth Routes
router.post('/login', authLimiter, validateBody(loginSchema), authController.login);

// Seller Onboarding Routes
router.post('/register', registrationLimiter, validateBody(registerSellerSchema), onboardingController.registerSeller);
router.post(
  '/documents/:sellerId',
  standardLimiter,
  validateParams(sellerIdParamSchema),
  protect,
  kycUpload.single('document'), // Accept a single file with field name 'document'
  onboardingController.uploadDocuments
);
router.post(
  '/verify',
  standardLimiter,
  validateBody(verificationStatusSchema),
  protect,
  onboardingController.updateVerificationStatus
);
router.post(
  '/activate/:sellerId',
  standardLimiter,
  validateParams(sellerIdParamSchema),
  protect,
  onboardingController.activateAccount
);
router.get(
  '/background-check/:sellerId',
  standardLimiter,
  validateParams(sellerIdParamSchema),
  protect,
  onboardingController.performBackgroundCheck
);

// KYC Routes
router.post('/kyc/submit', standardLimiter, protect, kycController.submitKYC);
router.get('/kyc/status', standardLimiter, protect, kycController.getKYCStatus);
router.get('/dashboard', standardLimiter, protect, kycController.getSellerDashboard);

// Admin KYC Routes (require admin permissions)
router.get('/kyc/admin/all', standardLimiter, protect, kycController.getAllSellersWithKYC);
router.put('/kyc/admin/verify/:sellerId', standardLimiter, validateParams(sellerIdParamSchema), protect, kycController.verifyKYCDocuments);
router.put('/admin/status', standardLimiter, protect, kycController.updateSellerStatus);

// Seller Profile Routes
router.get(
  '/profile',
  standardLimiter,
  protect,
  profileController.getProfile
);
router.put(
  '/profile',
  standardLimiter,
  validateBody(updateProfileSchema),
  protect,
  profileController.updateProfile
);
router.put(
  '/profile/bank-details',
  standardLimiter,
  validateBody(bankDetailsSchema),
  protect,
  profileController.updateBankDetails
);
router.get('/', standardLimiter, protect, profileController.getAllSellers);
router.delete(
  '/profile',
  standardLimiter,
  protect,
  profileController.deleteAccount
);

// Farm Management Routes
router.post(
  '/farms',
  standardLimiter,
  protect,
  farmManagementController.createFarm
);

router.get(
  '/farms',
  standardLimiter,
  protect,
  farmManagementController.getSellerFarms
);

router.get(
  '/farms/:farmId',
  standardLimiter,
  protect,
  farmManagementController.getFarmById
);

router.put(
  '/farms/:farmId',
  standardLimiter,
  protect,
  farmManagementController.updateFarm
);

router.put(
  '/farms/:farmId/rotation',
  standardLimiter,
  protect,
  farmManagementController.updateCropRotation
);

router.get(
  '/farms/:farmId/analytics',
  standardLimiter,
  protect,
  farmManagementController.getFarmAnalytics
);

router.get(
  '/farms/search',
  standardLimiter,
  protect,
  farmManagementController.searchFarms
);

router.delete(
  '/farms/:farmId',
  standardLimiter,
  protect,
  farmManagementController.deleteFarm
);

// Dropdown Routes
router.get(
  '/dropdowns/farms',
  standardLimiter,
  protect,
  dropdownController.getFarmsDropdown
);

router.get(
  '/dropdowns/farms/:sellerId',
  standardLimiter,
  protect,
  dropdownController.getFarmsDropdownBySeller
);

router.get(
  '/dropdowns/crops',
  standardLimiter,
  protect,
  dropdownController.getCropsDropdown
);

router.get(
  '/dropdowns/enums/:category/:subcategory',
  standardLimiter,
  protect,
  dropdownController.getEnumDropdown
);

router.get(
  '/dropdowns/enums',
  standardLimiter,
  protect,
  dropdownController.getAvailableEnums
);

export default router;