import { Request, Response } from 'express';
import { FarmManagementService } from '../services/farm-management.service';
import { 
  CreateFarmDto, 
  UpdateFarmDto, 
  FarmSearchDto,
  UpdateCropRotationDto
} from '../dto/farm-management.dto';
import { dbManager } from '@libs/shared';

export class FarmManagementController {
  private farmManagementService: FarmManagementService;

  constructor() {
    this.farmManagementService = new FarmManagementService(dbManager.getMongoDBConnector()!);
  }

  /**
   * Create a new farm
   */
  createFarm = async (req: Request, res: Response): Promise<void> => {
    try {
      // Get seller ID from the authenticated user's token instead of URL params
      if (!req.user || !req.user.id) {
        res.status(401).json({
          success: false,
          error: {
            code: 'AUTHENTICATION_ERROR',
            message: 'Authentication required'
          }
        });
        return;
      }

      const sellerId = req.user.id;
      const farmDto: CreateFarmDto = req.body;

      const farm = await this.farmManagementService.createFarm(sellerId, farmDto);

      res.status(201).json({
        success: true,
        data: farm,
        message: 'Farm created successfully'
      });
    } catch (error) {
      console.error('Error creating farm:', error);

      if (error.message === 'Seller not found') {
        res.status(404).json({
          success: false,
          error: {
            code: 'NOT_FOUND',
            message: 'Seller not found'
          }
        });
      } else {
        res.status(500).json({
          success: false,
          error: {
            code: 'INTERNAL_ERROR',
            message: 'Failed to create farm'
          }
        });
      }
    }
  };

  /**
   * Get farm by ID
   */
  getFarmById = async (req: Request, res: Response): Promise<void> => {
    try {
      const { farmId } = req.params;

      if (!farmId) {
        res.status(400).json({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Farm ID is required'
          }
        });
        return;
      }

      const farm = await this.farmManagementService.getFarmById(farmId);

      if (!farm) {
        res.status(404).json({
          success: false,
          error: {
            code: 'NOT_FOUND',
            message: 'Farm not found'
          }
        });
        return;
      }

      res.status(200).json({
        success: true,
        data: farm
      });
    } catch (error) {
      console.error('Error getting farm:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Failed to get farm'
        }
      });
    }
  };

  /**
   * Get all farms for a seller
   */
  getSellerFarms = async (req: Request, res: Response): Promise<void> => {
    try {
      // Get seller ID from the authenticated user's token instead of URL params
      if (!req.user || !req.user.id) {
        res.status(401).json({
          success: false,
          error: {
            code: 'AUTHENTICATION_ERROR',
            message: 'Authentication required'
          }
        });
        return;
      }

      const sellerId = req.user.id;
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 10;

      if (page < 1 || limit < 1 || limit > 100) {
        res.status(400).json({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Invalid pagination parameters'
          }
        });
        return;
      }

      const result = await this.farmManagementService.getSellerFarms(sellerId, page, limit);

      res.status(200).json({
        success: true,
        data: result
      });
    } catch (error) {
      console.error('Error getting seller farms:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Failed to get seller farms'
        }
      });
    }
  };

  /**
   * Update farm information
   */
  updateFarm = async (req: Request, res: Response): Promise<void> => {
    try {
      const { farmId } = req.params;
      const updateDto: UpdateFarmDto = req.body;

      if (!farmId) {
        res.status(400).json({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Farm ID is required'
          }
        });
        return;
      }

      const farm = await this.farmManagementService.updateFarm(farmId, updateDto);

      if (!farm) {
        res.status(404).json({
          success: false,
          error: {
            code: 'NOT_FOUND',
            message: 'Farm not found'
          }
        });
        return;
      }

      res.status(200).json({
        success: true,
        data: farm,
        message: 'Farm updated successfully'
      });
    } catch (error) {
      console.error('Error updating farm:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Failed to update farm'
        }
      });
    }
  };

  /**
   * Update crop rotation plan
   */
  updateCropRotation = async (req: Request, res: Response): Promise<void> => {
    try {
      const { farmId } = req.params;
      const rotationDto: UpdateCropRotationDto = req.body;

      if (!farmId) {
        res.status(400).json({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Farm ID is required'
          }
        });
        return;
      }

      const farm = await this.farmManagementService.updateCropRotation(farmId, rotationDto);

      if (!farm) {
        res.status(404).json({
          success: false,
          error: {
            code: 'NOT_FOUND',
            message: 'Farm not found'
          }
        });
        return;
      }

      res.status(200).json({
        success: true,
        data: farm,
        message: 'Crop rotation plan updated successfully'
      });
    } catch (error) {
      console.error('Error updating crop rotation:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Failed to update crop rotation plan'
        }
      });
    }
  };

  /**
   * Get farm analytics
   */
  getFarmAnalytics = async (req: Request, res: Response): Promise<void> => {
    try {
      const { farmId } = req.params;

      if (!farmId) {
        res.status(400).json({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Farm ID is required'
          }
        });
        return;
      }

      const analytics = await this.farmManagementService.getFarmAnalytics(farmId);

      if (!analytics) {
        res.status(404).json({
          success: false,
          error: {
            code: 'NOT_FOUND',
            message: 'Farm not found'
          }
        });
        return;
      }

      res.status(200).json({
        success: true,
        data: analytics
      });
    } catch (error) {
      console.error('Error getting farm analytics:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Failed to get farm analytics'
        }
      });
    }
  };

  /**
   * Search farms
   */
  searchFarms = async (req: Request, res: Response): Promise<void> => {
    try {
      const searchDto: FarmSearchDto = {
        sellerId: req.query.sellerId as string,
        location: {
          state: req.query.state as string,
          city: req.query.city as string,
          pincode: req.query.pincode as string,
        },
        soilType: req.query.soilType as string,
        waterSource: req.query.waterSource as string,
        farmingMethod: req.query.farmingMethod as any,
        minArea: req.query.minArea ? parseFloat(req.query.minArea as string) : undefined,
        maxArea: req.query.maxArea ? parseFloat(req.query.maxArea as string) : undefined,
        certifications: req.query.certifications ? (req.query.certifications as string).split(',') : undefined,
        status: req.query.status as any,
        page: parseInt(req.query.page as string) || 1,
        limit: parseInt(req.query.limit as string) || 10,
        sortBy: req.query.sortBy as any || 'createdAt',
        sortOrder: req.query.sortOrder as any || 'desc'
      };

      const result = await this.farmManagementService.searchFarms(searchDto);

      res.status(200).json({
        success: true,
        data: result
      });
    } catch (error) {
      console.error('Error searching farms:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Failed to search farms'
        }
      });
    }
  };

  /**
   * Delete farm
   */
  deleteFarm = async (req: Request, res: Response): Promise<void> => {
    try {
      const { farmId } = req.params;

      if (!farmId) {
        res.status(400).json({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Farm ID is required'
          }
        });
        return;
      }

      const deleted = await this.farmManagementService.deleteFarm(farmId);

      if (!deleted) {
        res.status(404).json({
          success: false,
          error: {
            code: 'NOT_FOUND',
            message: 'Farm not found'
          }
        });
        return;
      }

      res.status(200).json({
        success: true,
        message: 'Farm deleted successfully'
      });
    } catch (error) {
      console.error('Error deleting farm:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Failed to delete farm'
        }
      });
    }
  };
}
