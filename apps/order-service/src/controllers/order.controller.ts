import { Request, Response } from 'express';
import { OrderService } from '../services/order.service';
import { CreateOrderDto, UpdateOrderDto, OrderQueryDto } from '../dto/order.dto';
import { OrderStatus, PaymentStatus } from '../models/order.model';

export class OrderController {
  private orderService: OrderService;

  constructor(esClient?: any) {
    this.orderService = new OrderService(esClient);
  }

  /**
   * Create a new order
   */
  createOrder = async (req: Request, res: Response): Promise<void> => {
    try {
      const orderData: CreateOrderDto = req.body;

      // Validate required fields
      if (!orderData.buyerId || !orderData.sellerId || !orderData.items || orderData.items.length === 0) {
        res.status(400).json({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Missing required fields: buyerId, sellerId, and items are required'
          },
          timestamp: new Date().toISOString()
        });
        return;
      }

      const order = await this.orderService.createOrder(orderData);

      res.status(201).json({
        success: true,
        data: order,
        message: 'Order created successfully',
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Error in createOrder:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_SERVER_ERROR',
          message: error.message || 'Failed to create order'
        },
        timestamp: new Date().toISOString()
      });
    }
  };

  /**
   * Get order by ID
   */
  getOrderById = async (req: Request, res: Response): Promise<void> => {
    try {
      const { orderId } = req.params;

      const order = await this.orderService.getOrderById(orderId);

      if (!order) {
        res.status(404).json({
          success: false,
          error: {
            code: 'ORDER_NOT_FOUND',
            message: 'Order not found'
          },
          timestamp: new Date().toISOString()
        });
        return;
      }

      res.status(200).json({
        success: true,
        data: order,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Error in getOrderById:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_SERVER_ERROR',
          message: error.message || 'Failed to fetch order'
        },
        timestamp: new Date().toISOString()
      });
    }
  };

  /**
   * Get order by order number
   */
  getOrderByNumber = async (req: Request, res: Response): Promise<void> => {
    try {
      const { orderNumber } = req.params;

      const order = await this.orderService.getOrderByNumber(orderNumber);

      if (!order) {
        res.status(404).json({
          success: false,
          error: {
            code: 'ORDER_NOT_FOUND',
            message: 'Order not found'
          },
          timestamp: new Date().toISOString()
        });
        return;
      }

      res.status(200).json({
        success: true,
        data: order,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Error in getOrderByNumber:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_SERVER_ERROR',
          message: error.message || 'Failed to fetch order'
        },
        timestamp: new Date().toISOString()
      });
    }
  };

  /**
   * Update order
   */
  updateOrder = async (req: Request, res: Response): Promise<void> => {
    try {
      const { orderId } = req.params;
      const updateData: UpdateOrderDto = req.body;

      const order = await this.orderService.updateOrder(orderId, updateData);

      if (!order) {
        res.status(404).json({
          success: false,
          error: {
            code: 'ORDER_NOT_FOUND',
            message: 'Order not found'
          },
          timestamp: new Date().toISOString()
        });
        return;
      }

      res.status(200).json({
        success: true,
        data: order,
        message: 'Order updated successfully',
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Error in updateOrder:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_SERVER_ERROR',
          message: error.message || 'Failed to update order'
        },
        timestamp: new Date().toISOString()
      });
    }
  };

  /**
   * Get orders with filtering and pagination
   */
  getOrders = async (req: Request, res: Response): Promise<void> => {
    try {
      // Authentication check
      if (!req.user) {
        res.status(401).json({
          success: false,
          error: {
            code: 'UNAUTHORIZED',
            message: 'Authentication required'
          },
          timestamp: new Date().toISOString()
        });
        return;
      }

      // Determine sellerId based on user role
      let sellerId: string | undefined;

      if (req.user.role === 'admin') {
        // Admin can explicitly specify sellerId via query parameter
        sellerId = req.query.sellerId as string;
      } else if (req.user.role === 'seller') {
        // Seller can only access their own orders - use ID from JWT token
        sellerId = req.user.id;
      } else {
        // Other roles (like buyer) don't filter by sellerId
        sellerId = undefined;
      }

      const query: OrderQueryDto = {
        buyerId: req.query.buyerId as string,
        sellerId: sellerId,
        farmId: req.query.farmId as string,
        orderStatus: req.query.orderStatus as OrderStatus,
        paymentStatus: req.query.paymentStatus as PaymentStatus,
        startDate: req.query.startDate ? new Date(req.query.startDate as string) : undefined,
        endDate: req.query.endDate ? new Date(req.query.endDate as string) : undefined,
        page: req.query.page ? parseInt(req.query.page as string) : 1,
        limit: req.query.limit ? parseInt(req.query.limit as string) : 10,
        sortBy: req.query.sortBy as string || 'createdAt',
        sortOrder: (req.query.sortOrder as 'asc' | 'desc') || 'desc'
      };

      const result = await this.orderService.getOrders(query);

      res.status(200).json({
        success: true,
        data: result.orders,
        pagination: {
          total: result.total,
          page: result.page,
          limit: result.limit,
          totalPages: result.totalPages
        },
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Error in getOrders:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_SERVER_ERROR',
          message: error.message || 'Failed to fetch orders'
        },
        timestamp: new Date().toISOString()
      });
    }
  };

  /**
   * Get order statistics
   */
  getOrderStats = async (req: Request, res: Response): Promise<void> => {
    try {
      // Authentication check
      if (!req.user) {
        res.status(401).json({
          success: false,
          error: {
            code: 'UNAUTHORIZED',
            message: 'Authentication required'
          },
          timestamp: new Date().toISOString()
        });
        return;
      }

      // Determine sellerId based on user role
      let sellerId: string | undefined;

      if (req.user.role === 'admin') {
        // Admin can explicitly specify sellerId via query parameter
        sellerId = req.query.sellerId as string;
      } else if (req.user.role === 'seller') {
        // Seller can only access their own order stats - use ID from JWT token
        sellerId = req.user.id;
      } else {
        // Other roles (like buyer) don't filter by sellerId
        sellerId = undefined;
      }

      const stats = await this.orderService.getOrderStats(sellerId);

      res.status(200).json({
        success: true,
        data: stats,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Error in getOrderStats:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_SERVER_ERROR',
          message: error.message || 'Failed to fetch order statistics'
        },
        timestamp: new Date().toISOString()
      });
    }
  };

  /**
   * Cancel order
   */
  cancelOrder = async (req: Request, res: Response): Promise<void> => {
    try {
      const { orderId } = req.params;
      const { reason } = req.body;

      const order = await this.orderService.cancelOrder(orderId, reason);

      if (!order) {
        res.status(404).json({
          success: false,
          error: {
            code: 'ORDER_NOT_FOUND',
            message: 'Order not found'
          },
          timestamp: new Date().toISOString()
        });
        return;
      }

      res.status(200).json({
        success: true,
        data: order,
        message: 'Order cancelled successfully',
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Error in cancelOrder:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_SERVER_ERROR',
          message: error.message || 'Failed to cancel order'
        },
        timestamp: new Date().toISOString()
      });
    }
  };

  /**
   * Update order status
   */
  updateOrderStatus = async (req: Request, res: Response): Promise<void> => {
    try {
      const { orderId } = req.params;
      const { orderStatus, paymentStatus, trackingNumber, notes } = req.body;

      const updateData: UpdateOrderDto = {};

      if (orderStatus) updateData.orderStatus = orderStatus;
      if (paymentStatus) updateData.paymentStatus = paymentStatus;
      if (trackingNumber) updateData.trackingNumber = trackingNumber;
      if (notes) updateData.notes = notes;

      // Set delivery date if order is delivered
      if (orderStatus === OrderStatus.DELIVERED) {
        updateData.actualDeliveryDate = new Date();
      }

      const order = await this.orderService.updateOrder(orderId, updateData);

      if (!order) {
        res.status(404).json({
          success: false,
          error: {
            code: 'ORDER_NOT_FOUND',
            message: 'Order not found'
          },
          timestamp: new Date().toISOString()
        });
        return;
      }

      res.status(200).json({
        success: true,
        data: order,
        message: 'Order status updated successfully',
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Error in updateOrderStatus:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_SERVER_ERROR',
          message: error.message || 'Failed to update order status'
        },
        timestamp: new Date().toISOString()
      });
    }
  };
}
