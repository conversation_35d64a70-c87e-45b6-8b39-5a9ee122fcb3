import { CropGrowthStage, CropHealthStatus } from '../../../../libs/shared/database-connectors/schemas/mongo/crop.schema';

/**
 * Data Transfer Objects for Crop Service
 */

/**
 * DTO for creating a new crop
 */
export class CreateCropDto {
  numberOfPlots: number;
  farmId: string;
  sellerId: string;
  name: string;
  type: string;
  variety: string;
  plantingDate: Date;
  expectedHarvestDate: Date;
  actualHarvestDate?: Date;
  growthStage?: CropGrowthStage;
  health?: {
    status: CropHealthStatus;
    issues: string[];
    lastCheck: Date;
  };
  yield?: {
    expected: number;
    actual: number;
    unit: string;
  };
  resources?: {
    water: number;
    fertilizer: number;
    pesticides: number;
  };
  soilConditions: {
    type: string;
    ph: number;
    nutrients: string[];
  };
  waterAvailability: string;
  metadata: {
    cropCategory: string;
    farmingMethod: string;
    irrigationMethod: string;
    harvestSeason: string;
    pestDiseaseStatus?: string;
    storageMethod?: string;
    nutrientManagement?: string;
    waterSource: string;
    pesticideUsage?: string;
    seedType: string;
    harvestingMethod?: string;
  };
  nutritionalInfo?: {
    calories?: number;
    protein?: number;
    carbs?: number;
    fiber?: number;
    vitamins?: string[];
  };
  cultivation: {
    irrigationNeeds: string;
    fertilizerRequirements: string;
    pestControl: string;
    climateConditions: string;
  };
  postHarvest?: {
    storageRequirements: string;
    shelfLife: string;
    processingMethods: string[];
  };
  sustainability?: {
    waterUsage: string;
    carbonFootprint: string;
    pesticide: string;
  };
  maintenance?: {
    schedule?: {
      irrigation?: Date[];
      fertilization?: Date[];
      pestControl?: Date[];
      inspection?: Date[];
    };
    history?: {
      activities?: {
        type: string;
        date: Date;
        description: string;
        performedBy: string;
      }[];
    };
  };
  images?: string[];
  tags?: string[];
  certifications?: string[];
}

/**
 * DTO for updating an existing crop
 */
export class UpdateCropDto {
  name?: string;
  type?: string;
  variety?: string;
  plantingDate?: Date;
  expectedHarvestDate?: Date;
  actualHarvestDate?: Date;
  growthStage?: CropGrowthStage;
  health?: {
    status?: CropHealthStatus;
    issues?: string[];
    lastCheck?: Date;
  };
  yield?: {
    expected?: number;
    actual?: number;
    unit?: string;
  };
  resources?: {
    water?: number;
    fertilizer?: number;
    pesticides?: number;
  };
  soilConditions?: {
    type?: string;
    ph?: number;
    nutrients?: string[];
  };
  waterAvailability?: string;
  metadata?: {
    cropCategory?: string;
    farmingMethod?: string;
    irrigationMethod?: string;
    harvestSeason?: string;
    pestDiseaseStatus?: string;
    storageMethod?: string;
    nutrientManagement?: string;
    waterSource?: string;
    pesticideUsage?: string;
    seedType?: string;
    harvestingMethod?: string;
  };
  nutritionalInfo?: {
    calories?: number;
    protein?: number;
    carbs?: number;
    fiber?: number;
    vitamins?: string[];
  };
  cultivation?: {
    irrigationNeeds?: string;
    fertilizerRequirements?: string;
    pestControl?: string;
    climateConditions?: string;
  };
  postHarvest?: {
    storageRequirements?: string;
    shelfLife?: string;
    processingMethods?: string[];
  };
  sustainability?: {
    waterUsage?: string;
    carbonFootprint?: string;
    pesticide?: string;
  };
  maintenance?: {
    schedule?: {
      irrigation?: Date[];
      fertilization?: Date[];
      pestControl?: Date[];
      inspection?: Date[];
    };
    history?: {
      activities?: {
        type: string;
        date: Date;
        description: string;
        performedBy: string;
      }[];
    };
  };
  images?: string[];
  tags?: string[];
  certifications?: string[];
}

/**
 * DTO for querying crops
 */
export class QueryCropsDto {
  sellerId?: string;
  farmId?: string;
  type?: string;
  growthStage?: CropGrowthStage;
  healthStatus?: CropHealthStatus;
  harvestDateFrom?: Date;
  harvestDateTo?: Date;
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

/**
 * DTO for crop analytics
 */
export class CropAnalyticsDto {
  sellerId: string;
  farmId?: string;
  fromDate?: Date;
  toDate?: Date;
} 