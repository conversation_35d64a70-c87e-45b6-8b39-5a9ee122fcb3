import express, { Router } from 'express';
import { CropController } from '../controllers/crop.controller';
import { Client } from '@elastic/elasticsearch';
import { CropService } from '../services/crop.service';

/**
 * Setup crop routes
 * @param esClient - Elasticsearch client
 * @returns Express router
 */
export function setupCropRoutes(esClient: Client): Router {
  const router = express.Router();
  const cropService = new CropService(esClient);
  const cropController = new CropController(cropService);

  // Search route must come before parameterized routes to avoid conflicts
  router.get('/search', (req, res) => cropController.searchCrops(req, res));

  // Test and health routes
  router.get('/test-search', (req, res) => cropController.testSearch(req, res));
  router.get('/health', (req, res) => cropController.getHealthStatus(req, res));

  router.post('/', (req, res) => cropController.createCrop(req, res));

  router.get('/:cropId', (req, res) => cropController.getCropById(req, res));

  router.put('/:cropId', (req, res) => cropController.updateCrop(req, res));

  router.delete('/:cropId', (req, res) => cropController.deleteCrop(req, res));

  router.get('/', (req, res) => cropController.queryCrops(req, res));

  router.get('/analytics/:sellerId', (req, res) => cropController.getCropAnalytics(req, res));

  router.post('/reindex', (req, res) => cropController.reindexAllCrops(req, res));

  // Crop lifecycle management routes
  router.put('/:cropId/growth-stage', (req, res) => cropController.updateCropGrowthStage(req, res));

  router.put('/:cropId/health', (req, res) => cropController.updateCropHealth(req, res));

  router.post('/:cropId/harvest', (req, res) => cropController.recordHarvest(req, res));

  router.post('/:cropId/maintenance', (req, res) => cropController.addMaintenanceActivity(req, res));

  return router;
} 