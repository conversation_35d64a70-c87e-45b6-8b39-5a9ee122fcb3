import { v4 as uuidv4 } from 'uuid';
import { Client } from '@elastic/elasticsearch';
import mongoose from 'mongoose';
import { CreateCropDto, UpdateCropDto, QueryCropsDto, CropAnalyticsDto } from '../dto/crop.dto';
import { CropModel, ICrop, CropGrowthStage, CropHealthStatus } from '../../../../libs/shared/database-connectors/schemas/mongo/crop.schema';
import { CropDocument } from '../../../../libs/shared/database-connectors/schemas/elastic/crop.schema';
import { CropSearchService, CropSearchFilters, CropSearchOptions } from '../../../../libs/shared/database-connectors/services/crop-search.service';

/**
 * Service class for crop operations
 */
export class CropService {
  private esClient: Client;
  private cropModel: mongoose.Model<ICrop>;
  private readonly INDEX_NAME = process.env.ELASTICSEARCH_INDEX || 'befarma';

  /**
   * Constructor for CropService
   * @param esClient - Elasticsearch client
   */
  constructor(esClient: Client) {
    this.esClient = esClient;
    this.cropModel = CropModel;
  }

  /**
   * Create a new crop
   * @param createCropDto - Data to create a crop
   * @returns The created crop
   */
  async createCrop(createCropDto: CreateCropDto): Promise<ICrop> {
    try {
      // Generate a unique cropId
      const cropId = uuidv4();
      
      console.log('Creating crop with data:');

      // Prepare crop data with defaults for MongoDB
      const cropData = {
        ...createCropDto,
        cropId,
        growthStage: createCropDto.growthStage || CropGrowthStage.PLANTING,
        health: createCropDto.health || {
          status: CropHealthStatus.HEALTHY,
          issues: [],
          lastCheck: new Date()
        }
      };
      
      // Create the crop in MongoDB
      const crop = new this.cropModel(cropData);
      const savedCrop = await crop.save();
      
      // Index the crop in Elasticsearch
      await this.indexCropInElasticsearch(savedCrop);
      
      return savedCrop;
    } catch (error) {
      console.error('Error creating crop:', error);
      throw error;
    }
  }

  /**
   * Update a crop by ID
   * @param cropId - ID of the crop to update
   * @param updateCropDto - Data to update the crop
   * @returns The updated crop
   */
  async updateCrop(cropId: string, updateCropDto: UpdateCropDto): Promise<ICrop | null> {
    try {
      // Update the crop in MongoDB
      const updatedCrop = await this.cropModel.findOneAndUpdate(
        { cropId },
        { $set: updateCropDto },
        { new: true }
      );

      if (!updatedCrop) {
        return null;
      }

      // Try to update the crop in Elasticsearch, but don't fail if ES is unavailable
      try {
        await this.updateCropInElasticsearch(updatedCrop);
      } catch (esError) {
        console.warn(`Failed to update crop ${cropId} in Elasticsearch:`, esError.message);
        console.log('Crop was successfully updated in MongoDB, continuing...');
        // Don't throw error - MongoDB update was successful
      }

      return updatedCrop;
    } catch (error) {
      console.error('Error updating crop in MongoDB:', error);
      throw error;
    }
  }

  /**
   * Delete a crop by ID
   * @param cropId - ID of the crop to delete
   * @returns Boolean indicating success
   */
  async deleteCrop(cropId: string): Promise<boolean> {
    try {
      // Delete the crop from MongoDB first
      const deletionResult = await this.cropModel.deleteOne({ cropId });

      if (deletionResult.deletedCount === 0) {
        return false;
      }

      // Try to delete from Elasticsearch, but don't fail if ES is unavailable
      try {
        await this.esClient.delete({
          index: this.INDEX_NAME,
          id: cropId
        });
        console.log(`Successfully deleted crop ${cropId} from Elasticsearch`);
      } catch (esError) {
        console.warn(`Failed to delete crop ${cropId} from Elasticsearch:`, esError.message);
        console.log('Crop was successfully deleted from MongoDB, continuing...');
        // Don't throw error - MongoDB deletion was successful
      }

      return true;
    } catch (error) {
      console.error('Error deleting crop from MongoDB:', error);
      throw error;
    }
  }

  /**
   * Get a crop by ID
   * @param cropId - ID of the crop to retrieve
   * @returns The crop data or null
   */
  async getCropById(cropId: string): Promise<ICrop | null> {
    try {
      return await this.cropModel.findOne({ cropId });
    } catch (error) {
      console.error('Error getting crop by ID:', error);
      throw error;
    }
  }

  /**
   * Query crops with filters
   * @param queryDto - Query parameters
   * @returns Array of crops and total count
   */
  async queryCrops(queryDto: QueryCropsDto): Promise<{ crops: ICrop[]; total: number }> {
    try {
      const {
        sellerId,
        farmId,
        type,
        growthStage,
        healthStatus,
        harvestDateFrom,
        harvestDateTo,
        page = 1,
        limit = 10,
        sortBy = 'createdAt',
        sortOrder = 'desc'
      } = queryDto;
      
      // Build the query filter
      const filter: any = {};
      
      if (sellerId) filter.sellerId = sellerId;
      if (farmId) filter.farmId = farmId;
      if (type) filter.type = type;
      if (growthStage) filter.growthStage = growthStage;
      if (healthStatus) filter['health.status'] = healthStatus;
      
      // Date range filter for harvest
      if (harvestDateFrom || harvestDateTo) {
        filter.expectedHarvestDate = {};
        if (harvestDateFrom) filter.expectedHarvestDate.$gte = harvestDateFrom;
        if (harvestDateTo) filter.expectedHarvestDate.$lte = harvestDateTo;
      }
      
      // Calculate pagination
      const skip = (page - 1) * limit;
      
      // Prepare sort options
      const sort: any = {};
      sort[sortBy] = sortOrder === 'asc' ? 1 : -1;
      
      // Execute query with pagination
      const crops = await this.cropModel
        .find(filter)
        .sort(sort)
        .skip(skip)
        .limit(limit);
      
      // Get total count for pagination
      const total = await this.cropModel.countDocuments(filter);
      
      return { crops, total };
    } catch (error) {
      console.error('Error querying crops:', error);
      throw error;
    }
  }

  /**
   * Search crops using Elasticsearch
   * @param query - Search query
   * @param filters - Additional filters
   * @param page - Page number
   * @param limit - Results per page
   * @returns Search results
   */
  async searchCrops(
    query: string,
    filters: any = {},
    page: number = 1,
    limit: number = 10
  ): Promise<{ crops: any[]; total: number }> {
    try {
      // First, verify Elasticsearch connection
      await this.verifyElasticsearchConnection();

      const from = (page - 1) * limit;

      // Build Elasticsearch query
      const esQuery: any = {
        bool: {
          must: [
            {
              multi_match: {
                query,
                fields: ['name^2', 'type^1.5', 'variety', 'attributes.cropCategory', 'attributes.tags'],
                fuzziness: 'AUTO',
                operator: 'or'
              }
            }
          ],
          filter: []
        }
      };

      // Add filters
      if (filters.sellerId) {
        esQuery.bool.filter.push({ term: { sellerId: filters.sellerId } });
      }

      if (filters.farmId) {
        esQuery.bool.filter.push({ term: { farmId: filters.farmId } });
      }

      if (filters.growthStage) {
        esQuery.bool.filter.push({ term: { growthStage: filters.growthStage } });
      }

      if (filters.healthStatus) {
        esQuery.bool.filter.push({ term: { 'healthStatus.status': filters.healthStatus } });
      }

      // Date range filter
      if (filters.harvestDateFrom || filters.harvestDateTo) {
        const rangeFilter: any = { range: { expectedHarvestDate: {} } };

        if (filters.harvestDateFrom) {
          rangeFilter.range.expectedHarvestDate.gte = filters.harvestDateFrom;
        }

        if (filters.harvestDateTo) {
          rangeFilter.range.expectedHarvestDate.lte = filters.harvestDateTo;
        }

        esQuery.bool.filter.push(rangeFilter);
      }

      console.log('Executing Elasticsearch query:', JSON.stringify(esQuery, null, 2));

      // Execute search
      const result = await this.esClient.search({
        index: this.INDEX_NAME,
        query: esQuery,
        from,
        size: limit,
        sort: [
          { _score: { order: 'desc' } },
          { createdAt: { order: 'desc' } }
        ]
      });

      const hits = result.hits.hits;
      const total = result.hits.total as any;

      console.log(`Elasticsearch search returned ${hits.length} results out of ${typeof total === 'object' ? total.value : total} total`);

      // Extract results
      const crops = hits.map((hit: any) => ({
        ...hit._source,
        score: hit._score
      }));

      return {
        crops,
        total: typeof total === 'object' ? total.value : total
      };
    } catch (error) {
      console.error('Error searching crops in Elasticsearch:', error);

      // Fallback to MongoDB search if Elasticsearch fails
      console.log('Falling back to MongoDB search...');
      return this.fallbackToMongoDBSearch(query, filters, page, limit);
    }
  }

  /**
   * Verify Elasticsearch connection
   */
  private async verifyElasticsearchConnection(): Promise<void> {
    try {
      const pingResult = await this.esClient.ping();
      if (!pingResult) {
        throw new Error('Elasticsearch ping failed');
      }

      // Check if index exists
      const indexExists = await this.esClient.indices.exists({
        index: this.INDEX_NAME
      });

      if (!indexExists) {
        console.warn(`Elasticsearch index '${this.INDEX_NAME}' does not exist. Creating index...`);
        await this.createElasticsearchIndex();
      }
    } catch (error) {
      console.error('Elasticsearch connection verification failed:', error);
      throw new Error(`Elasticsearch is not available: ${error.message}`);
    }
  }

  /**
   * Create Elasticsearch index with proper mappings
   */
  private async createElasticsearchIndex(): Promise<void> {
    try {
      await this.esClient.indices.create({
        index: this.INDEX_NAME,
        mappings: {
          properties: {
            cropId: { type: 'keyword' },
            sellerId: { type: 'keyword' },
            farmId: { type: 'keyword' },
            numberOfPlots: { type: 'integer' },
            name: {
              type: 'text',
              fields: {
                keyword: { type: 'keyword' }
              }
            },
            type: { type: 'keyword' },
            variety: { type: 'keyword' },
            growthStage: { type: 'keyword' },
            expectedHarvestDate: { type: 'date' },
            createdAt: { type: 'date' },
            'attributes.cropCategory': { type: 'keyword' },
            'attributes.tags': { type: 'keyword' },
            'healthStatus.status': { type: 'keyword' }
          }
        }
      });
      console.log(`Elasticsearch index '${this.INDEX_NAME}' created successfully`);
    } catch (error) {
      console.error('Error creating Elasticsearch index:', error);
      throw error;
    }
  }

  /**
   * Fallback search using MongoDB when Elasticsearch is unavailable
   */
  private async fallbackToMongoDBSearch(
    query: string,
    filters: any = {},
    page: number = 1,
    limit: number = 10
  ): Promise<{ crops: any[]; total: number }> {
    try {
      const skip = (page - 1) * limit;

      // Build MongoDB query
      const mongoQuery: any = {
        $and: []
      };

      // Text search
      if (query && query.trim()) {
        mongoQuery.$and.push({
          $or: [
            { name: { $regex: query, $options: 'i' } },
            { type: { $regex: query, $options: 'i' } },
            { variety: { $regex: query, $options: 'i' } },
            { 'metadata.cropCategory': { $regex: query, $options: 'i' } },
            { tags: { $in: [new RegExp(query, 'i')] } }
          ]
        });
      }

      // Add filters
      if (filters.sellerId) {
        mongoQuery.$and.push({ sellerId: filters.sellerId });
      }

      if (filters.farmId) {
        mongoQuery.$and.push({ farmId: filters.farmId });
      }

      if (filters.growthStage) {
        mongoQuery.$and.push({ growthStage: filters.growthStage });
      }

      if (filters.healthStatus) {
        mongoQuery.$and.push({ 'health.status': filters.healthStatus });
      }

      // Date range filter
      if (filters.harvestDateFrom || filters.harvestDateTo) {
        const dateFilter: any = {};

        if (filters.harvestDateFrom) {
          dateFilter.$gte = new Date(filters.harvestDateFrom);
        }

        if (filters.harvestDateTo) {
          dateFilter.$lte = new Date(filters.harvestDateTo);
        }

        mongoQuery.$and.push({ expectedHarvestDate: dateFilter });
      }

      // If no conditions, search all
      const finalQuery = mongoQuery.$and.length > 0 ? mongoQuery : {};

      const [crops, total] = await Promise.all([
        this.cropModel.find(finalQuery)
          .sort({ createdAt: -1 })
          .skip(skip)
          .limit(limit)
          .lean(),
        this.cropModel.countDocuments(finalQuery)
      ]);

      console.log(`MongoDB fallback search returned ${crops.length} results out of ${total} total`);

      return { crops, total };
    } catch (error) {
      console.error('MongoDB fallback search failed:', error);
      throw error;
    }
  }

  /**
   * Get service health status including Elasticsearch connection
   */
  async getHealthStatus(): Promise<any> {
    const status = {
      service: 'crop-service',
      timestamp: new Date().toISOString(),
      mongodb: {
        status: 'unknown',
        connected: false
      },
      elasticsearch: {
        status: 'unknown',
        connected: false,
        index: this.INDEX_NAME,
        indexExists: false
      }
    };

    try {
      // Check MongoDB connection
      const mongoState = this.cropModel.db.readyState;
      status.mongodb.connected = mongoState === 1;
      status.mongodb.status = mongoState === 1 ? 'connected' : 'disconnected';
    } catch (error) {
      status.mongodb.status = 'error';
      console.error('MongoDB health check failed:', error);
    }

    try {
      // Check Elasticsearch connection
      const pingResult = await this.esClient.ping();
      status.elasticsearch.connected = !!pingResult;
      status.elasticsearch.status = pingResult ? 'connected' : 'disconnected';

      // Check if index exists
      if (pingResult) {
        const indexExists = await this.esClient.indices.exists({
          index: this.INDEX_NAME
        });
        status.elasticsearch.indexExists = indexExists;
      }
    } catch (error) {
      status.elasticsearch.status = 'error';
      console.error('Elasticsearch health check failed:', error);
    }

    return status;
  }

  /**
   * Get crop analytics
   * @param analyticsDto - Analytics query parameters
   * @returns Analytics data
   */
  async getCropAnalytics(analyticsDto: CropAnalyticsDto): Promise<any> {
    try {
      const { sellerId, farmId, fromDate, toDate } = analyticsDto;
      
      // Build filter
      const filter: any = { sellerId };
      if (farmId) filter.farmId = farmId;
      
      // Date filter
      if (fromDate || toDate) {
        filter.createdAt = {};
        if (fromDate) filter.createdAt.$gte = fromDate;
        if (toDate) filter.createdAt.$lte = toDate;
      }
      
      // Get total crops count
      const totalCrops = await this.cropModel.countDocuments(filter);
      
      // Get crops by type
      const cropsByType = await this.cropModel.aggregate([
        { $match: filter },
        { $group: { _id: '$type', count: { $sum: 1 } } },
        { $project: { _id: 0, type: '$_id', count: 1 } }
      ]);
      
      // Get crops by growth stage
      const cropsByGrowthStage = await this.cropModel.aggregate([
        { $match: filter },
        { $group: { _id: '$growthStage', count: { $sum: 1 } } },
        { $project: { _id: 0, growthStage: '$_id', count: 1 } }
      ]);
      
      // Get crops by health status
      const cropsByHealthStatus = await this.cropModel.aggregate([
        { $match: filter },
        { $group: { _id: '$health.status', count: { $sum: 1 } } },
        { $project: { _id: 0, healthStatus: '$_id', count: 1 } }
      ]);
      
      // Get upcoming harvests
      const upcomingHarvests = await this.cropModel
        .find({
          ...filter,
          expectedHarvestDate: { $gte: new Date() }
        })
        .sort({ expectedHarvestDate: 1 })
        .limit(10)
        .select('cropId name expectedHarvestDate');
      
      // Get resource usage
      const resourceUsage = await this.cropModel.aggregate([
        { $match: filter },
        {
          $group: {
            _id: null,
            totalWater: { $sum: '$resources.water' },
            totalFertilizer: { $sum: '$resources.fertilizer' },
            totalPesticides: { $sum: '$resources.pesticides' }
          }
        },
        { $project: { _id: 0 } }
      ]);
      
      // Format and return analytics
      return {
        totalCrops,
        cropsByType: cropsByType.reduce((acc: any, curr: any) => {
          acc[curr.type] = curr.count;
          return acc;
        }, {}),
        cropsByGrowthStage: cropsByGrowthStage.reduce((acc: any, curr: any) => {
          acc[curr.growthStage] = curr.count;
          return acc;
        }, {}),
        cropsByHealthStatus: cropsByHealthStatus.reduce((acc: any, curr: any) => {
          acc[curr.healthStatus] = curr.count;
          return acc;
        }, {}),
        upcomingHarvests,
        resourceUsage: resourceUsage[0] || { totalWater: 0, totalFertilizer: 0, totalPesticides: 0 }
      };
    } catch (error) {
      console.error('Error getting crop analytics:', error);
      throw error;
    }
  }

  /**
   * Index a crop in Elasticsearch
   * @param crop - Crop to index
   */
  private async indexCropInElasticsearch(crop: ICrop): Promise<void> {
    try {
      const document: CropDocument = this.convertToElasticsearchDocument(crop);

      await this.esClient.index({
        index: this.INDEX_NAME,
        id: crop.cropId,
        document: document,
        refresh: true
      });

      console.log(`Successfully indexed crop ${crop.cropId} in Elasticsearch`);
    } catch (error) {
      console.error('Error indexing crop in Elasticsearch:', error);
      console.error('Error details:', {
        message: error.message,
        statusCode: error.statusCode,
        cropId: crop.cropId
      });

      // Check if it's an authentication error
      if (error.statusCode === 401 || error.message.includes('security_exception')) {
        console.error('Elasticsearch authentication failed - crop saved to MongoDB only');
        console.warn('Please check Elasticsearch credentials and configuration');
        // Don't throw error - allow MongoDB-only operation
        return;
      }

      // For other errors, still don't throw to allow MongoDB-only operation
      console.warn('Elasticsearch indexing failed - continuing with MongoDB-only operation');
    }
  }

  /**
   * Update a crop in Elasticsearch
   * @param crop - Updated crop data
   */
  private async updateCropInElasticsearch(crop: ICrop): Promise<void> {
    try {
      const document: CropDocument = this.convertToElasticsearchDocument(crop);

      await this.esClient.update({
        index: this.INDEX_NAME,
        id: crop.cropId,
        doc: document,
        refresh: true
      });

      console.log(`Successfully updated crop ${crop.cropId} in Elasticsearch`);
    } catch (error) {
      console.error('Error updating crop in Elasticsearch:', error);
      console.error('Error details:', {
        message: error.message,
        statusCode: error.statusCode,
        cropId: crop.cropId
      });

      // Check if it's an authentication error
      if (error.statusCode === 401 || error.message.includes('security_exception')) {
        console.error('Elasticsearch authentication failed - crop updated in MongoDB only');
        console.warn('Please check Elasticsearch credentials and configuration');
        // Don't throw error - allow MongoDB-only operation
        return;
      }

      // For other errors, still don't throw to allow MongoDB-only operation
      console.warn('Elasticsearch update failed - continuing with MongoDB-only operation');
    }
  }

  /**
   * Convert MongoDB crop document to Elasticsearch document
   * @param crop - MongoDB crop document
   * @returns Elasticsearch crop document
   */
  private convertToElasticsearchDocument(crop: ICrop): CropDocument {
    return {
      id: crop._id.toString(),
      cropId: crop.cropId,
      numberOfPlots: crop.numberOfPlots,
      farmId: crop.farmId,
      sellerId: crop.sellerId,
      name: crop.name,
      type: crop.type,
      variety: crop.variety,
      plantingDate: crop.plantingDate.toISOString(),
      expectedHarvestDate: crop.expectedHarvestDate.toISOString(),
      actualHarvestDate: crop.actualHarvestDate?.toISOString(),
      growthStage: crop.growthStage,
      healthStatus: {
        status: crop.health.status,
        issues: crop.health.issues,
        lastCheck: crop.health.lastCheck.toISOString()
      },
      yield: crop.yield,
      resources: crop.resources,
      // Add missing required properties
      seller: {
        sellerId: crop.sellerId,
        name: '', // These fields need to be populated from seller data
        contact: '',
        email: '',
        location: {
          country: '',
          state: '',
          city: '',
          pincode: '',
          addressLine1: '',
          addressLine2: ''
        },
        certifications: []
      },
      farm: {
        farmId: crop.farmId,
        name: '', // These fields need to be populated from farm data
        location: {
          country: '',
          state: '',
          city: '',
          pincode: '',
          addressLine1: '',
          addressLine2: ''
        },
        totalArea: 0,
        soilType: '',
        waterSource: '',
        infrastructure: [],
        certifications: []
      },
      product: {
        isForSale: false,
        price: {
          amount: 0,
          currency: 'INR',
          unit: 'kg',
          negotiable: false
        },
        availability: {
          status: 'AVAILABLE',
          quantity: 0,
          unit: ''
        },
        delivery: {
          methods: [],
          charges: 0,
          estimatedTime: '',
          radius: 0
        },
        qualityGrade: 'STANDARD',
        packaging: {
          type: '',
          sizes: [],
          customPackaging: false
        }
      },
      attributes: {
        images: crop.images,
        tags: crop.tags,
        cropCategory: crop.metadata.cropCategory,
        farmingMethod: crop.metadata.farmingMethod,
        irrigationMethod: crop.metadata.irrigationMethod,
        harvestSeason: crop.metadata.harvestSeason,
        pestDiseaseStatus: crop.metadata.pestDiseaseStatus,
        storageMethod: crop.metadata.storageMethod,
        nutrientManagement: crop.metadata.nutrientManagement,
        waterSource: crop.metadata.waterSource,
        pesticideUsage: crop.metadata.pesticideUsage,
        seedType: crop.metadata.seedType,
        harvestingMethod: crop.metadata.harvestingMethod,
        soilConditions: crop.soilConditions,
        waterAvailability: crop.waterAvailability,
        nutritionalInfo: crop.nutritionalInfo,
        cultivation: {
          ...crop.cultivation,
          irrigationNeeds: crop.cultivation.irrigationNeeds,
          fertilizerRequirements: crop.cultivation.fertilizerRequirements,
          pestControl: crop.cultivation.pestControl,
          climateConditions: crop.cultivation.climateConditions,
          sowingMethod: '',
          spacingRequirements: '',
          companionCrops: []
        },
        postHarvest: crop.postHarvest,
        sustainability: crop.sustainability
      },
      maintenance: {
        schedule: {
          irrigation: crop.maintenance.schedule.irrigation.map(date => date.toISOString()),
          fertilization: crop.maintenance.schedule.fertilization.map(date => date.toISOString()),
          pestControl: crop.maintenance.schedule.pestControl.map(date => date.toISOString()),
          inspection: crop.maintenance.schedule.inspection.map(date => date.toISOString())
        },
        history: {
          activities: crop.maintenance.history.activities.map(activity => ({
            ...activity,
            date: activity.date.toISOString()
          })) as any
        }
      },
      weather: {
        forecasts: crop.weather.forecasts.map(forecast => ({
          ...forecast,
          date: forecast.date.toISOString()
        })) as any,
        alerts: crop.weather.alerts.map(alert => ({
          ...alert,
          startDate: alert.startDate.toISOString(),
          endDate: alert.endDate.toISOString()
        })) as any
      },
      certifications: crop.certifications,
      createdAt: crop.createdAt.toISOString(),
      updatedAt: crop.updatedAt.toISOString()
    };
  }

  /**
   * Update crop growth stage
   * @param cropId - ID of the crop
   * @param newStage - New growth stage
   * @param notes - Optional notes about the stage change
   * @returns Updated crop
   */
  async updateCropGrowthStage(
    cropId: string,
    newStage: CropGrowthStage,
    notes?: string
  ): Promise<ICrop | null> {
    try {
      const crop = await this.cropModel.findOne({ cropId });

      if (!crop) {
        return null;
      }

      // Update growth stage and add to maintenance log
      const updatedCrop = await this.cropModel.findOneAndUpdate(
        { cropId },
        {
          $set: { growthStage: newStage },
          $push: {
            'maintenance.activities': {
              activityId: uuidv4(),
              type: 'GROWTH_STAGE_UPDATE',
              date: new Date(),
              description: `Growth stage updated to ${newStage}`,
              notes: notes || '',
              cost: 0,
              resources: []
            }
          }
        },
        { new: true }
      );

      if (updatedCrop) {
        await this.updateCropInElasticsearch(updatedCrop);
      }

      return updatedCrop;
    } catch (error) {
      console.error('Error updating crop growth stage:', error);
      throw error;
    }
  }

  /**
   * Update crop health status
   * @param cropId - ID of the crop
   * @param healthStatus - New health status
   * @param issues - Health issues
   * @param notes - Optional notes
   * @returns Updated crop
   */
  async updateCropHealth(
    cropId: string,
    healthStatus: CropHealthStatus,
    issues: string[] = [],
    notes?: string
  ): Promise<ICrop | null> {
    try {
      const updatedCrop = await this.cropModel.findOneAndUpdate(
        { cropId },
        {
          $set: {
            'health.status': healthStatus,
            'health.issues': issues,
            'health.lastCheck': new Date()
          },
          $push: {
            'maintenance.activities': {
              activityId: uuidv4(),
              type: 'HEALTH_CHECK',
              date: new Date(),
              description: `Health status updated to ${healthStatus}`,
              notes: notes || `Issues: ${issues.join(', ')}`,
              cost: 0,
              resources: []
            }
          }
        },
        { new: true }
      );

      if (updatedCrop) {
        await this.updateCropInElasticsearch(updatedCrop);
      }

      return updatedCrop;
    } catch (error) {
      console.error('Error updating crop health:', error);
      throw error;
    }
  }

  /**
   * Record crop harvest
   * @param cropId - ID of the crop
   * @param actualYield - Actual yield harvested
   * @param harvestDate - Date of harvest
   * @param quality - Quality grade
   * @returns Updated crop
   */
  async recordHarvest(
    cropId: string,
    actualYield: number,
    harvestDate: Date = new Date(),
    quality?: string
  ): Promise<ICrop | null> {
    try {
      const updatedCrop = await this.cropModel.findOneAndUpdate(
        { cropId },
        {
          $set: {
            actualHarvestDate: harvestDate,
            'yield.actual': actualYield,
            growthStage: CropGrowthStage.READY
          },
          $push: {
            'maintenance.activities': {
              activityId: uuidv4(),
              type: 'HARVEST',
              date: harvestDate,
              description: `Crop harvested - Yield: ${actualYield}`,
              notes: quality ? `Quality: ${quality}` : '',
              cost: 0,
              resources: []
            }
          }
        },
        { new: true }
      );

      if (updatedCrop) {
        await this.updateCropInElasticsearch(updatedCrop);
      }

      return updatedCrop;
    } catch (error) {
      console.error('Error recording harvest:', error);
      throw error;
    }
  }

  /**
   * Add maintenance activity to crop
   * @param cropId - ID of the crop
   * @param activity - Maintenance activity details
   * @returns Updated crop
   */
  async addMaintenanceActivity(
    cropId: string,
    activity: {
      type: string;
      description: string;
      cost: number;
      resources: string[];
      notes?: string;
    }
  ): Promise<ICrop | null> {
    try {
      const updatedCrop = await this.cropModel.findOneAndUpdate(
        { cropId },
        {
          $push: {
            'maintenance.activities': {
              activityId: uuidv4(),
              type: activity.type,
              date: new Date(),
              description: activity.description,
              notes: activity.notes || '',
              cost: activity.cost,
              resources: activity.resources
            }
          },
          $inc: {
            'maintenance.totalCost': activity.cost
          }
        },
        { new: true }
      );

      if (updatedCrop) {
        await this.updateCropInElasticsearch(updatedCrop);
      }

      return updatedCrop;
    } catch (error) {
      console.error('Error adding maintenance activity:', error);
      throw error;
    }
  }

  /**
   * Reindex all crops from MongoDB to Elasticsearch
   */
  async reindexAllCrops(): Promise<void> {
    try {
      // Get all crops from MongoDB
      const crops = await this.cropModel.find();

      // Create index with mapping if it doesn't exist
      const indexExists = await this.esClient.indices.exists({ index: this.INDEX_NAME });

      if (!indexExists) {
        await this.esClient.indices.create({
          index: this.INDEX_NAME,
          settings: {
            number_of_shards: 3,
            number_of_replicas: 1,
            analysis: {
              analyzer: {
                crop_analyzer: {
                  type: 'custom',
                  tokenizer: 'standard',
                  filter: ['lowercase', 'asciifolding']
                }
              }
            }
          }
        });
      }

      // Bulk index all crops
      if (crops.length > 0) {
        const bulkBody: any[] = [];

        crops.forEach(crop => {
          bulkBody.push(
            { index: { _index: this.INDEX_NAME, _id: crop.cropId } },
            this.convertToElasticsearchDocument(crop)
          );
        });

        await this.esClient.bulk({
          refresh: true,
          operations: bulkBody
        });

        console.log(`Successfully reindexed ${crops.length} crops`);
      }
    } catch (error) {
      console.error('Error reindexing crops:', error);
      throw error;
    }
  }
}
