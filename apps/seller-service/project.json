{"name": "seller-service", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/seller-service/src", "projectType": "application", "targets": {"build": {"executor": "@nx/esbuild:esbuild", "outputs": ["{options.outputPath}"], "defaultConfiguration": "development", "options": {"platform": "node", "outputPath": "dist/seller-service", "format": ["cjs"], "bundle": true, "main": "apps/seller-service/src/main.ts", "tsConfig": "apps/seller-service/tsconfig.app.json", "assets": ["apps/seller-service/src/assets"], "generatePackageJson": true, "esbuildOptions": {"sourcemap": true, "outExtension": {".js": ".js"}}}, "configurations": {"development": {"fileReplacements": [{"replace": "apps/seller-service/.env", "with": "apps/seller-service/.env.development"}]}, "production": {"fileReplacements": [{"replace": "apps/seller-service/.env", "with": "apps/seller-service/.env.production"}]}}}, "serve": {"continuous": true, "executor": "@nx/js:node", "defaultConfiguration": "development", "dependsOn": ["build"], "options": {"buildTarget": "seller-service:build", "runBuildTargetDependencies": false}, "configurations": {"development": {"buildTarget": "seller-service:build:development"}, "production": {"buildTarget": "seller-service:build:production"}}}}, "tags": []}