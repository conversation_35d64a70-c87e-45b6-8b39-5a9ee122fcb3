/**
 * DTOs for farm management operations in seller service
 */

export interface CreateFarmDto {
  name: string;
  location: {
    country?: string;
    state: string;
    city: string;
    pincode: string;
    addressLine1: string;
    addressLine2?: string;
    coordinates?: {
      latitude: number;
      longitude: number;
    };
  };
  totalArea: number;
  soilType: string;
  waterSource: string;
  infrastructure: string[];
  certifications?: string[];
  farmingPractices?: {
    primaryMethod: 'ORGANIC' | 'CONVENTIONAL' | 'INTEGRATED';
    irrigationSystems: string[];
    sustainabilityScore?: number;
  };
}

export interface UpdateFarmDto {
  name?: string;
  location?: {
    country?: string;
    state?: string;
    city?: string;
    pincode?: string;
    addressLine1?: string;
    addressLine2?: string;
    coordinates?: {
      latitude?: number;
      longitude?: number;
    };
  };
  totalArea?: number;
  soilType?: string;
  waterSource?: string;
  infrastructure?: string[];
  certifications?: string[];
  farmingPractices?: {
    primaryMethod?: 'ORGANIC' | 'CONVENTIONAL' | 'INTEGRATED';
    irrigationSystems?: string[];
    sustainabilityScore?: number;
  };
}

export interface FarmResponseDto {
  farmId: string;
  sellerId: string;
  name: string;
  location: {
    country: string;
    state: string;
    city: string;
    pincode: string;
    addressLine1: string;
    addressLine2: string;
    coordinates?: {
      latitude: number;
      longitude: number;
    };
  };
  totalArea: number;
  soilType: string;
  waterSource: string;
  infrastructure: string[];
  certifications: string[];
  status: 'ACTIVE' | 'INACTIVE';
  currentCrops: {
    cropId: string;
    plotId: string;
    name: string;
    variety: string;
    plantingDate: Date;
    expectedHarvestDate: Date;
    growthStage: string;
    season: 'KHARIF' | 'RABI' | 'ZAID';
  }[];
  cropRotationPlan: {
    plotId: string;
    season: 'KHARIF' | 'RABI' | 'ZAID';
    year: number;
    plannedCrop: string;
    variety?: string;
    status: 'PLANNED' | 'PLANTED' | 'HARVESTED';
  }[];
  farmingPractices: {
    primaryMethod: 'ORGANIC' | 'CONVENTIONAL' | 'INTEGRATED';
    irrigationSystems: string[];
    sustainabilityScore?: number;
  };
  createdAt: Date;
  updatedAt: Date;
}

export interface CropRotationPlanDto {
  plotId: string;
  season: 'KHARIF' | 'RABI' | 'ZAID';
  year: number;
  plannedCrop: string;
  variety?: string;
}

export interface UpdateCropRotationDto {
  rotationPlan: CropRotationPlanDto[];
}

export interface FarmAnalyticsDto {
  farmId: string;
  totalArea: number;
  utilizationRate: number;
  currentCropsCount: number;
  plannedCropsCount: number;
  sustainabilityScore: number;
  seasonalBreakdown: {
    kharif: number;
    rabi: number;
    zaid: number;
  };
  cropDiversity: {
    cereals: number;
    vegetables: number;
    oilseeds: number;
    fiber: number;
    others: number;
  };
  recentActivity: {
    date: Date;
    activity: string;
    details: string;
  }[];
}

export interface FarmSearchDto {
  sellerId?: string;
  location?: {
    state?: string;
    city?: string;
    pincode?: string;
  };
  soilType?: string;
  waterSource?: string;
  farmingMethod?: 'ORGANIC' | 'CONVENTIONAL' | 'INTEGRATED';
  minArea?: number;
  maxArea?: number;
  certifications?: string[];
  status?: 'ACTIVE' | 'INACTIVE';
  page?: number;
  limit?: number;
  sortBy?: 'name' | 'totalArea' | 'createdAt' | 'sustainabilityScore';
  sortOrder?: 'asc' | 'desc';
}

export interface FarmListResponseDto {
  farms: FarmResponseDto[];
  total: number;
  page: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}
