import { SellerRepository, FarmRepository } from '@libs/shared';
import { KYCSubmissionDto, KYCVerificationDto, SellerStatusDto, SellerDashboardDto } from '../dto/kyc.dto';
import { MongoDBConnector } from '@libs/shared';

export class KYCService {
  private sellerRepository: SellerRepository;
  private farmRepository: FarmRepository;

  constructor(connector: MongoDBConnector) {
    this.sellerRepository = new SellerRepository(connector);
    this.farmRepository = new FarmRepository(connector);
  }

  /**
   * Submit KYC documents for verification
   */
  async submitKYCDocuments(kycData: KYCSubmissionDto): Promise<{ success: boolean; kycId?: string; message: string }> {
    try {
      const seller = await this.sellerRepository.findById(kycData.sellerId);
      
      if (!seller) {
        return {
          success: false,
          message: 'Seller not found'
        };
      }

      // Check if KYC is already verified
      if (seller.verificationStatus === 'VERIFIED') {
        return {
          success: false,
          message: 'Seller already verified'
        };
      }

      // Map the KYC personal details to seller schema format
      const addressMapping = {
        addressLine1: kycData.personalDetails.address.street,
        addressLine2: kycData.personalDetails.address.city,
        city: kycData.personalDetails.address.city,
        state: kycData.personalDetails.address.state,
        pincode: kycData.personalDetails.address.zipCode,
        country: kycData.personalDetails.address.country
      };

      // Update seller with KYC information
      const updateData = {
        personalInfo: {
          ...seller.personalInfo,
          name: kycData.personalDetails.fullName,
          address: addressMapping
        },
        documents: {
          identityProof: kycData.documents.find(doc => doc.documentType === 'aadhaar' || doc.documentType === 'pan')?.documentUrl || seller.documents?.identityProof,
          landOwnership: kycData.documents.find(doc => doc.documentType === 'address_proof')?.documentUrl || seller.documents?.landOwnership,
          certifications: kycData.documents.filter(doc => doc.documentType === 'bank_statement').map(doc => doc.documentUrl) || seller.documents?.certifications || []
        },
        bankDetails: kycData.personalDetails.bankDetails,
        verificationStatus: 'PENDING' as const,
        updatedAt: new Date()
      };

      const updatedSeller = await this.sellerRepository.update(kycData.sellerId, updateData);

      return {
        success: true,
        kycId: updatedSeller?._id.toString(),
        message: 'KYC documents submitted successfully'
      };
    } catch (error) {
      console.error('Error submitting KYC documents:', error);
      throw new Error('Failed to submit KYC documents');
    }
  }

  /**
   * Get KYC status for a seller
   */
  async getKYCStatus(sellerId: string): Promise<KYCVerificationDto | null> {
    try {
      const seller = await this.sellerRepository.findById(sellerId);
      
      if (!seller) {
        return null;
      }

      // Map seller documents to KYC documents format
      const documents = [];
      if (seller.documents?.identityProof) {
        documents.push({
          documentType: 'aadhaar' as const,
          documentNumber: 'XXXX-XXXX-XXXX',
          documentUrl: seller.documents.identityProof,
          isVerified: seller.verificationStatus === 'VERIFIED'
        });
      }
      if (seller.documents?.landOwnership) {
        documents.push({
          documentType: 'address_proof' as const,
          documentNumber: 'ADDRESS-PROOF',
          documentUrl: seller.documents.landOwnership,
          isVerified: seller.verificationStatus === 'VERIFIED'
        });
      }

      return {
        kycId: seller._id.toString(),
        sellerId: seller._id.toString(),
        status: seller.verificationStatus.toLowerCase() === 'verified' ? 'verified' 
               : seller.verificationStatus.toLowerCase() === 'rejected' ? 'rejected' 
               : 'pending',
        documents: documents
      };
    } catch (error) {
      console.error('Error getting KYC status:', error);
      throw new Error('Failed to get KYC status');
    }
  }

  /**
   * Update seller status (admin function)
   */
  async updateSellerStatus(statusData: SellerStatusDto): Promise<{ success: boolean; message: string }> {
    try {
      const seller = await this.sellerRepository.findById(statusData.sellerId);
      
      if (!seller) {
        return {
          success: false,
          message: 'Seller not found'
        };
      }

      // Map status values to schema enum
      const statusMapping = {
        'active': 'ACTIVE',
        'inactive': 'PENDING',
        'suspended': 'SUSPENDED',
        'pending_verification': 'PENDING'
      } as const;

      const newStatus = statusMapping[statusData.status] || 'PENDING';
      const now = new Date();
      // Prepare statusHistory entry
      const statusHistoryEntry = {
        status: newStatus,
        updatedBy: statusData.updatedBy,
        reason: statusData.reason,
        updatedAt: now,
      };

      // Push to statusHistory array
      const updateData = {
        status: newStatus,
        updatedAt: now,
        $push: { statusHistory: statusHistoryEntry }
      };

      await this.sellerRepository.update(statusData.sellerId, updateData);

      return {
        success: true,
        message: `Seller status updated to ${statusData.status}`
      };
    } catch (error) {
      console.error('Error updating seller status:', error);
      throw new Error('Failed to update seller status');
    }
  }

  /**
   * Get seller dashboard data with farm and crop information
   */
  async getSellerDashboard(sellerId: string): Promise<SellerDashboardDto | null> {
    try {
      const seller = await this.sellerRepository.findById(sellerId);

      if (!seller) {
        return null;
      }

      // Get farm data for the seller
      const farms = await this.farmRepository.findBySellerId(sellerId);

      // For now, use farm's currentCrops data (crops will be integrated later)
      let allCrops: any[] = [];
      farms.forEach(farm => {
        if (farm.currentCrops && farm.currentCrops.length > 0) {
          allCrops = allCrops.concat(farm.currentCrops);
        }
      });

      // Calculate farm and crop analytics
      const farmAnalytics = {
        totalFarms: farms.length,
        totalArea: farms.reduce((sum, farm) => sum + farm.totalArea, 0),
        activeFarms: farms.filter(farm => farm.status === 'ACTIVE').length,
        organicFarms: farms.filter(farm => farm.farmingPractices?.primaryMethod === 'ORGANIC').length,
        averageSustainabilityScore: farms.length > 0
          ? farms.reduce((sum, farm) => sum + (farm.farmingPractices?.sustainabilityScore || 0), 0) / farms.length
          : 0
      };

      const cropAnalytics = {
        totalCrops: allCrops.length,
        activeCrops: allCrops.filter(crop =>
          crop.growthStage !== 'HARVESTED'
        ).length,
        healthyCrops: allCrops.length, // Simplified for now
        criticalCrops: 0, // Simplified for now
        seasonalBreakdown: {
          kharif: allCrops.filter(crop => crop.season === 'KHARIF').length,
          rabi: allCrops.filter(crop => crop.season === 'RABI').length,
          zaid: allCrops.filter(crop => crop.season === 'ZAID').length
        },
        upcomingHarvests: allCrops.filter(crop => {
          const harvestDate = new Date(crop.expectedHarvestDate);
          const now = new Date();
          const thirtyDaysFromNow = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000);
          return harvestDate >= now && harvestDate <= thirtyDaysFromNow;
        }).length
      };

      // Enhanced performance data
      const performanceData = {
        totalOrders: 0, // Will be populated from order service
        totalRevenue: 0, // Will be populated from order service
        averageRating: 0, // Will be populated from review service
        totalReviews: 0, // Will be populated from review service
        farmAnalytics,
        cropAnalytics
      };

      // Enhanced recent activity with farm and crop activities
      const recentActivity = [
        {
          type: 'account',
          description: 'Profile updated',
          timestamp: new Date()
        }
      ];

      // Add recent farm activities
      farms.slice(0, 3).forEach(farm => {
        recentActivity.push({
          type: 'farm',
          description: `Farm "${farm.name}" - ${farm.currentCrops?.length || 0} active crops`,
          timestamp: farm.updatedAt
        });
      });

      // Add recent crop activities
      allCrops
        .slice(0, 3)
        .forEach(crop => {
          recentActivity.push({
            type: 'crop',
            description: `${crop.name} (${crop.variety}) - ${crop.growthStage}`,
            timestamp: new Date(crop.expectedHarvestDate)
          });
        });

      return {
        sellerId: seller._id.toString(),
        basicInfo: {
          name: seller.personalInfo.name,
          email: seller.personalInfo.email,
          phone: seller.personalInfo.contact,
          status: seller.status.toLowerCase(),
          kycStatus: seller.verificationStatus.toLowerCase(),
          joinDate: seller.createdAt
        },
        performance: performanceData,
        recentActivity: recentActivity.sort((a, b) =>
          new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
        ).slice(0, 10)
      };
    } catch (error) {
      console.error('Error getting seller dashboard:', error);
      throw new Error('Failed to get seller dashboard data');
    }
  }

  /**
   * Get all sellers with KYC status (admin function)
   */
  async getAllSellersWithKYC(page: number = 1, limit: number = 10): Promise<{
    sellers: KYCVerificationDto[];
    total: number;
    page: number;
    totalPages: number;
  }> {
    try {
      const result = await this.sellerRepository.findAll(page, limit);
      
      const sellersWithKYC: KYCVerificationDto[] = result.sellers.map(seller => {
        const documents = [];
        if (seller.documents?.identityProof) {
          documents.push({
            documentType: 'aadhaar' as const,
            documentNumber: 'XXXX-XXXX-XXXX',
            documentUrl: seller.documents.identityProof,
            isVerified: seller.verificationStatus === 'VERIFIED'
          });
        }

        const status: 'verified' | 'rejected' | 'pending' | 'in_review' = 
          seller.verificationStatus.toLowerCase() === 'verified' ? 'verified' 
          : seller.verificationStatus.toLowerCase() === 'rejected' ? 'rejected' 
          : 'pending';

        return {
          kycId: seller._id.toString(),
          sellerId: seller._id.toString(),
          status: status,
          documents: documents
        };
      });

      return {
        sellers: sellersWithKYC,
        total: result.total,
        page: page,
        totalPages: result.pages
      };
    } catch (error) {
      console.error('Error getting sellers with KYC:', error);
      throw new Error('Failed to get sellers with KYC status');
    }
  }

  /**
   * Verify KYC documents (admin function)
   */
  async verifyKYCDocuments(
    sellerId: string, 
    verifiedBy: string, 
    approved: boolean, 
    notes?: string
  ): Promise<{ success: boolean; message: string }> {
    try {
      const seller = await this.sellerRepository.findById(sellerId);
      
      if (!seller) {
        return {
          success: false,
          message: 'Seller not found'
        };
      }

      const updateData = {
        verificationStatus: approved ? 'VERIFIED' as const : 'REJECTED' as const,
        status: approved ? 'ACTIVE' as const : 'PENDING' as const,
        updatedAt: new Date()
      };

      await this.sellerRepository.update(sellerId, updateData);

      return {
        success: true,
        message: `KYC ${approved ? 'approved' : 'rejected'} successfully`
      };
    } catch (error) {
      console.error('Error verifying KYC documents:', error);
      throw new Error('Failed to verify KYC documents');
    }
  }
} 