import { 
  FarmRepository,
  SellerRepository,
  FarmDocument,
  MongoDBConnector,
  dbManager
} from '@libs/shared';
import { 
  CreateFarmDto, 
  UpdateFarmDto, 
  FarmResponseDto, 
  FarmAnalyticsDto,
  FarmSearchDto,
  FarmListResponseDto,
  UpdateCropRotationDto
} from '../dto/farm-management.dto';
import { v4 as uuidv4 } from 'uuid';

export class FarmManagementService {
  private farmRepository: FarmRepository;
  private sellerRepository: SellerRepository;
  
  constructor(dbConnector: MongoDBConnector) {
    this.farmRepository = new FarmRepository(dbConnector);
    this.sellerRepository = new SellerRepository(dbConnector);
  }
  
  /**
   * Create a new farm for a seller
   */
  async createFarm(sellerId: string, farmDto: CreateFarmDto): Promise<FarmResponseDto> {
    try {
      // Verify seller exists
      const seller = await this.sellerRepository.findById(sellerId);
      if (!seller) {
        throw new Error('Seller not found');
      }
      
      // Generate farm ID
      const farmId = `FARM-${uuidv4().substring(0, 8).toUpperCase()}`;
      
      // Create farm data
      const farmData = {
        farmId,
        sellerId,
        name: farmDto.name,
        location: {
          country: farmDto.location.country || 'INDIA',
          state: farmDto.location.state,
          city: farmDto.location.city,
          pincode: farmDto.location.pincode,
          addressLine1: farmDto.location.addressLine1,
          addressLine2: farmDto.location.addressLine2 || '',
          coordinates: farmDto.location.coordinates
        },
        totalArea: farmDto.totalArea,
        soilType: farmDto.soilType,
        waterSource: farmDto.waterSource,
        infrastructure: farmDto.infrastructure,
        certifications: farmDto.certifications || [],
        status: 'ACTIVE' as const,
        plots: [],
        currentCrops: [] as any,
        cropRotationPlan: [] as any,
        farmingPractices: farmDto.farmingPractices || {
          primaryMethod: 'CONVENTIONAL',
          irrigationSystems: [],
          sustainabilityScore: 0
        }
      };
      
      // Create farm
      const farm = await this.farmRepository.create(farmData);
      
      // Update seller's farms array
      await this.sellerRepository.addFarm(sellerId, farm._id.toString());
      
      return this.mapToResponseDto(farm);
    } catch (error) {
      console.error('Error creating farm:', error);
      throw error;
    }
  }
  
  /**
   * Get farm by ID
   */
  async getFarmById(farmId: string): Promise<FarmResponseDto | null> {
    try {
      const farm = await this.farmRepository.findById(farmId);
      return farm ? this.mapToResponseDto(farm) : null;
    } catch (error) {
      console.error('Error getting farm by ID:', error);
      throw error;
    }
  }
  
  /**
   * Get all farms for a seller
   */
  async getSellerFarms(sellerId: string, page: number = 1, limit: number = 10): Promise<FarmListResponseDto> {
    try {
      const farms = await this.farmRepository.findBySellerId(sellerId);
      const total = farms.length;
      const startIndex = (page - 1) * limit;
      const endIndex = startIndex + limit;
      const paginatedFarms = farms.slice(startIndex, endIndex);

      return {
        farms: paginatedFarms.map(farm => this.mapToResponseDto(farm)),
        total,
        page,
        totalPages: Math.ceil(total / limit),
        hasNext: page < Math.ceil(total / limit),
        hasPrev: page > 1
      };
    } catch (error) {
      console.error('Error getting seller farms:', error);
      throw error;
    }
  }
  
  /**
   * Update farm information
   */
  async updateFarm(farmId: string, updateDto: UpdateFarmDto): Promise<FarmResponseDto | null> {
    try {
      const farm = await this.farmRepository.findById(farmId);
      if (!farm) {
        throw new Error('Farm not found');
      }
      
      // Build update object
      const updateData: any = {};
      
      if (updateDto.name) updateData.name = updateDto.name;
      if (updateDto.totalArea) updateData.totalArea = updateDto.totalArea;
      if (updateDto.soilType) updateData.soilType = updateDto.soilType;
      if (updateDto.waterSource) updateData.waterSource = updateDto.waterSource;
      if (updateDto.infrastructure) updateData.infrastructure = updateDto.infrastructure;
      if (updateDto.certifications) updateData.certifications = updateDto.certifications;
      if (updateDto.farmingPractices) updateData.farmingPractices = updateDto.farmingPractices;
      
      if (updateDto.location) {
        updateData.location = { ...farm.location, ...updateDto.location };
      }
      
      const updatedFarm = await this.farmRepository.update(farmId, updateData);
      return updatedFarm ? this.mapToResponseDto(updatedFarm) : null;
    } catch (error) {
      console.error('Error updating farm:', error);
      throw error;
    }
  }
  
  /**
   * Update crop rotation plan
   */
  async updateCropRotation(farmId: string, rotationDto: UpdateCropRotationDto): Promise<FarmResponseDto | null> {
    try {
      const farm = await this.farmRepository.findById(farmId);
      if (!farm) {
        throw new Error('Farm not found');
      }

      const updatedFarm = await this.farmRepository.update(farmId, {
        cropRotationPlan: rotationDto.rotationPlan as any
      });
      
      return updatedFarm ? this.mapToResponseDto(updatedFarm) : null;
    } catch (error) {
      console.error('Error updating crop rotation:', error);
      throw error;
    }
  }
  
  /**
   * Get farm analytics
   */
  async getFarmAnalytics(farmId: string): Promise<FarmAnalyticsDto | null> {
    try {
      const farm = await this.farmRepository.findById(farmId);
      if (!farm) {
        return null;
      }
      
      // Calculate analytics
      const currentCropsCount = farm.currentCrops?.length || 0;
      const plannedCropsCount = farm.cropRotationPlan?.length || 0;
      const utilizationRate = farm.totalArea > 0 ? (currentCropsCount / farm.totalArea) * 100 : 0;
      
      // Seasonal breakdown
      const seasonalBreakdown = {
        kharif: farm.currentCrops?.filter(crop => crop.season === 'KHARIF').length || 0,
        rabi: farm.currentCrops?.filter(crop => crop.season === 'RABI').length || 0,
        zaid: farm.currentCrops?.filter(crop => crop.season === 'ZAID').length || 0
      };
      
      // Crop diversity (simplified calculation)
      const cropDiversity = {
        cereals: 0,
        vegetables: 0,
        oilseeds: 0,
        fiber: 0,
        others: 0
      };
      
      return {
        farmId: farm.farmId,
        totalArea: farm.totalArea,
        utilizationRate,
        currentCropsCount,
        plannedCropsCount,
        sustainabilityScore: farm.farmingPractices?.sustainabilityScore || 0,
        seasonalBreakdown,
        cropDiversity,
        recentActivity: [
          {
            date: farm.updatedAt,
            activity: 'Farm Updated',
            details: 'Farm information was last updated'
          }
        ]
      };
    } catch (error) {
      console.error('Error getting farm analytics:', error);
      throw error;
    }
  }
  
  /**
   * Search farms with filters
   */
  async searchFarms(searchDto: FarmSearchDto): Promise<FarmListResponseDto> {
    try {
      // Build search criteria
      const criteria: any = {};
      
      if (searchDto.sellerId) criteria.sellerId = searchDto.sellerId;
      if (searchDto.soilType) criteria.soilType = searchDto.soilType;
      if (searchDto.waterSource) criteria.waterSource = searchDto.waterSource;
      if (searchDto.status) criteria.status = searchDto.status;
      
      if (searchDto.location) {
        if (searchDto.location.state) criteria['location.state'] = searchDto.location.state;
        if (searchDto.location.city) criteria['location.city'] = searchDto.location.city;
        if (searchDto.location.pincode) criteria['location.pincode'] = searchDto.location.pincode;
      }
      
      if (searchDto.minArea || searchDto.maxArea) {
        criteria.totalArea = {};
        if (searchDto.minArea) criteria.totalArea.$gte = searchDto.minArea;
        if (searchDto.maxArea) criteria.totalArea.$lte = searchDto.maxArea;
      }
      
      if (searchDto.farmingMethod) {
        criteria['farmingPractices.primaryMethod'] = searchDto.farmingMethod;
      }
      
      if (searchDto.certifications && searchDto.certifications.length > 0) {
        criteria.certifications = { $in: searchDto.certifications };
      }
      
      const page = searchDto.page || 1;
      const limit = searchDto.limit || 10;
      const sortBy = searchDto.sortBy || 'createdAt';
      const sortOrder = searchDto.sortOrder === 'asc' ? 1 : -1;
      
      // For now, use findAll and filter in memory (can be optimized later)
      const allFarmsResult = await this.farmRepository.findAll(1, 1000);
      let filteredFarms = allFarmsResult.farms;

      // Apply filters
      if (searchDto.sellerId) {
        filteredFarms = filteredFarms.filter(farm => farm.sellerId === searchDto.sellerId);
      }
      if (searchDto.soilType) {
        filteredFarms = filteredFarms.filter(farm => farm.soilType === searchDto.soilType);
      }
      if (searchDto.waterSource) {
        filteredFarms = filteredFarms.filter(farm => farm.waterSource === searchDto.waterSource);
      }
      if (searchDto.status) {
        filteredFarms = filteredFarms.filter(farm => farm.status === searchDto.status);
      }

      // Apply pagination
      const total = filteredFarms.length;
      const startIndex = (page - 1) * limit;
      const endIndex = startIndex + limit;
      const paginatedFarms = filteredFarms.slice(startIndex, endIndex);

      return {
        farms: paginatedFarms.map(farm => this.mapToResponseDto(farm)),
        total,
        page,
        totalPages: Math.ceil(total / limit),
        hasNext: page < Math.ceil(total / limit),
        hasPrev: page > 1
      };
    } catch (error) {
      console.error('Error searching farms:', error);
      throw error;
    }
  }
  
  /**
   * Delete farm
   */
  async deleteFarm(farmId: string): Promise<boolean> {
    try {
      const farm = await this.farmRepository.findById(farmId);
      if (!farm) {
        throw new Error('Farm not found');
      }

      // Remove farm reference from seller
      await this.sellerRepository.removeFarm(farm.sellerId, farm._id.toString());

      // Delete farm
      return await this.farmRepository.delete(farmId);
    } catch (error) {
      console.error('Error deleting farm:', error);
      throw error;
    }
  }
  
  /**
   * Map farm document to response DTO
   */
  private mapToResponseDto(farm: FarmDocument): FarmResponseDto {
    return {
      farmId: farm.farmId,
      sellerId: farm.sellerId,
      name: farm.name,
      location: farm.location,
      totalArea: farm.totalArea,
      soilType: farm.soilType,
      waterSource: farm.waterSource,
      infrastructure: farm.infrastructure,
      certifications: farm.certifications,
      status: farm.status,
      currentCrops: farm.currentCrops || [],
      cropRotationPlan: farm.cropRotationPlan || [],
      farmingPractices: farm.farmingPractices || {
        primaryMethod: 'CONVENTIONAL',
        irrigationSystems: [],
        sustainabilityScore: 0
      },
      createdAt: farm.createdAt,
      updatedAt: farm.updatedAt
    };
  }
}
