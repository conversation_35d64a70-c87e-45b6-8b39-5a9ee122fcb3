import { SellerBankDetailsDto, UpdateSellerProfileDto } from '../dto/seller.dto';
import { 
  SellerRepository,
  SellerDocument,
  MongoDBConnector,
  dbManager
} from '@libs/shared';

export class SellerProfileService {
  private sellerRepository: SellerRepository;
  
  constructor(dbConnector: MongoDBConnector) {
    this.sellerRepository = new SellerRepository(dbConnector);
  }
  
  /**
   * Get seller profile by ID
   * @param sellerId Seller ID
   * @returns Seller profile
   */
  async getProfile(sellerId: string): Promise<SellerDocument | null> {
    try {
      return await this.sellerRepository.findById(sellerId);
    } catch (error) {
      console.error('Error getting seller profile:', error);
      throw error;
    }
  }
  
  /**
   * Update seller profile
   * @param sellerId Seller ID
   * @param profileDto Profile update data
   * @returns Updated seller profile
   */
  async updateProfile(sellerId: string, profileDto: UpdateSellerProfileDto): Promise<SellerDocument | null> {
    try {
      // Check if seller exists
      const seller = await this.sellerRepository.findById(sellerId);
      if (!seller) {
        throw new Error('Seller not found');
      }
      
      // Create update object with only the fields that are provided
      const updateData: any = {};
      
      if (profileDto.name) {
        updateData['personalInfo.name'] = profileDto.name;
      }
      
      if (profileDto.contact) {
        updateData['personalInfo.contact'] = profileDto.contact;
      }
      
      if (profileDto.email) {
        // Check if the new email is already in use by another seller
        if (profileDto.email !== seller.personalInfo.email) {
          const existingSeller = await this.sellerRepository.findByEmail(profileDto.email);
          if (existingSeller && existingSeller.sellerId !== sellerId) {
            throw new Error('Email already in use by another seller');
          }
        }
        updateData['personalInfo.email'] = profileDto.email;
      }
      
      if (profileDto.address) {
        if (profileDto.address.country) {
          updateData['personalInfo.address.country'] = profileDto.address.country;
        }
        if (profileDto.address.state) {
          updateData['personalInfo.address.state'] = profileDto.address.state;
        }
        if (profileDto.address.city) {
          updateData['personalInfo.address.city'] = profileDto.address.city;
        }
        if (profileDto.address.pincode) {
          updateData['personalInfo.address.pincode'] = profileDto.address.pincode;
        }
        if (profileDto.address.addressLine1) {
          updateData['personalInfo.address.addressLine1'] = profileDto.address.addressLine1;
        }
        if (profileDto.address.addressLine2) {
          updateData['personalInfo.address.addressLine2'] = profileDto.address.addressLine2;
        }
      }
      
      // Return updated seller
      return await this.sellerRepository.update(sellerId, updateData);
    } catch (error) {
      console.error('Error updating seller profile:', error);
      throw error;
    }
  }
  
  /**
   * Update seller bank details
   * @param sellerId Seller ID
   * @param bankDetailsDto Bank details data
   * @returns Updated seller profile
   */
  async updateBankDetails(sellerId: string, bankDetailsDto: SellerBankDetailsDto): Promise<SellerDocument | null> {
    try {
      // Check if seller exists
      const seller = await this.sellerRepository.findById(sellerId);
      if (!seller) {
        throw new Error('Seller not found');
      }
      
      // Update bank details
      return await this.sellerRepository.update(sellerId, {
        bankDetails: {
          accountNumber: bankDetailsDto.accountNumber,
          bankName: bankDetailsDto.bankName,
          ifscCode: bankDetailsDto.ifscCode,
        },
      });
    } catch (error) {
      console.error('Error updating bank details:', error);
      throw error;
    }
  }
  
  /**
   * Get all sellers with pagination
   * @param page Page number
   * @param limit Items per page
   * @returns Paginated sellers
   */
  async getAllSellers(page: number = 1, limit: number = 10): Promise<{ sellers: SellerDocument[]; total: number; pages: number }> {
    try {
      return await this.sellerRepository.findAll(page, limit);
    } catch (error) {
      console.error('Error getting all sellers:', error);
      throw error;
    }
  }
  
  /**
   * Delete seller account
   * @param sellerId Seller ID
   * @returns True if deleted, false otherwise
   */
  async deleteAccount(sellerId: string): Promise<boolean> {
    try {
      return await this.sellerRepository.delete(sellerId);
    } catch (error) {
      console.error('Error deleting seller account:', error);
      throw error;
    }
  }
} 