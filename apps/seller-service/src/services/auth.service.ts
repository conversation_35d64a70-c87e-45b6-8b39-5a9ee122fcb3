import { SellerLoginDto } from '../dto/seller.dto';
import * as bcrypt from 'bcrypt';
import * as jwt from 'jsonwebtoken';
import { 
  SellerDocument, 
  SellerRepository,
  MongoDBConnector
} from '@libs/shared';

export class AuthService {
  private sellerRepository: SellerRepository;
  private jwtSecret: string;
  
  constructor(dbConnector: MongoDBConnector) {
    this.sellerRepository = new SellerRepository(dbConnector);
    this.jwtSecret = process.env.JWT_SECRET || 'befarma_';
  }
  
  /**
   * Login a seller
   * @param loginDto Login data
   * @returns JWT token and seller data
   */
  async login(loginDto: SellerLoginDto): Promise<{ token: string; seller: Partial<SellerDocument> }> {
    try {
      // Find seller by email with password for authentication
      const seller = await this.sellerRepository.findByEmailWithPassword(loginDto.email);
      
      if (!seller) {
        throw new Error('Invalid email or password');
      }
      
      // Check password - now properly checking against bcrypted password in database
      if (!(seller as any).password) {
        throw new Error('Password not set for this account. Please contact support.');
      }

      const isPasswordValid = await bcrypt.compare(loginDto.password, (seller as any).password);
      if (!isPasswordValid) {
        throw new Error('Invalid email or password');
      }
      
      // Check if seller account is active
      if (seller.status !== 'ACTIVE') {
        throw new Error('Your account is not active. Please contact support.');
      }
      
      // Generate JWT token
      const token = this.generateToken(seller);
      
      // Return token and seller data (without sensitive info)
      return {
        token,
        seller: {
          sellerId: seller.sellerId,
          personalInfo: {
            name: seller.personalInfo.name,
            email: seller.personalInfo.email,
            contact: seller.personalInfo.contact,
            address: seller.personalInfo.address,
          },
          status: seller.status,
          verificationStatus: seller.verificationStatus,
        },
      };
    } catch (error) {
      console.error('Error in login:', error);
      throw error;
    }
  }
  
  /**
   * Generate JWT token
   * @param seller Seller document
   * @returns JWT token
   */
  private generateToken(seller: SellerDocument): string {
    const payload = {
      id: seller.sellerId,
      email: seller.personalInfo.email,
      name: seller.personalInfo.name,
      role: 'seller',
    };
    
    return jwt.sign(payload, this.jwtSecret, {
      expiresIn: '1d', // Token expires in 1 day
    });
  }
  
  /**
   * Verify JWT token
   * @param token JWT token
   * @returns Decoded token payload
   */
  verifyToken(token: string): any {
    try {
      return jwt.verify(token, this.jwtSecret);
    } catch (error) {
      console.error('Error verifying token:', error);
      throw new Error('Invalid token');
    }
  }
} 