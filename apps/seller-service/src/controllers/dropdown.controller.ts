import { Request, Response } from 'express';
import { DropdownService } from '../services/dropdown.service';
import { DropdownQueryDto, DropdownResponseDto } from '../dto/dropdown.dto';
import { dbManager } from '@libs/shared';
import httpStatus from 'http-status';

/**
 * Controller for dropdown listing endpoints
 */
export class DropdownController {
  private dropdownService: DropdownService;

  constructor() {
    this.dropdownService = new DropdownService(dbManager.getMongoDBConnector()!);
  }

  /**
   * Get farms dropdown for authenticated seller
   */
  getFarmsDropdown = async (req: Request, res: Response): Promise<void> => {
    try {
      // Extract seller ID from authenticated user
      const sellerId = req.user?.id;
      
      if (!sellerId) {
        res.status(httpStatus.UNAUTHORIZED).json({
          success: false,
          error: {
            code: 'UNAUTHORIZED',
            message: 'User not authenticated'
          },
          timestamp: new Date().toISOString()
        });
        return;
      }

      // Extract query parameters
      const queryDto: DropdownQueryDto = {
        status: req.query.status as string,
        search: req.query.search as string,
        limit: req.query.limit ? parseInt(req.query.limit as string, 10) : 50
      };

      const { farms, total } = await this.dropdownService.getFarmsDropdown(sellerId, queryDto);

      const response: DropdownResponseDto<any> = {
        success: true,
        data: farms,
        total,
        message: 'Farms dropdown retrieved successfully'
      };

      res.status(httpStatus.OK).json(response);
    } catch (error: any) {
      console.error('Error getting farms dropdown:', error);
      
      res.status(httpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        error: {
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to get farms dropdown',
          details: error.message
        },
        timestamp: new Date().toISOString()
      });
    }
  };

  /**
   * Get farms dropdown for specific seller (admin access)
   */
  getFarmsDropdownBySeller = async (req: Request, res: Response): Promise<void> => {
    try {
      const { sellerId } = req.params;
      const userRole = req.user?.role;

      // Check if user is admin or the seller themselves
      if (userRole !== 'admin' && req.user?.id !== sellerId) {
        res.status(httpStatus.FORBIDDEN).json({
          success: false,
          error: {
            code: 'FORBIDDEN',
            message: 'Access denied. Admin role required or must be the seller themselves.'
          },
          timestamp: new Date().toISOString()
        });
        return;
      }

      if (!sellerId) {
        res.status(httpStatus.BAD_REQUEST).json({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Seller ID is required'
          },
          timestamp: new Date().toISOString()
        });
        return;
      }

      // Extract query parameters
      const queryDto: DropdownQueryDto = {
        status: req.query.status as string,
        search: req.query.search as string,
        limit: req.query.limit ? parseInt(req.query.limit as string, 10) : 50
      };

      const { farms, total } = await this.dropdownService.getFarmsDropdown(sellerId, queryDto);

      const response: DropdownResponseDto<any> = {
        success: true,
        data: farms,
        total,
        message: 'Farms dropdown retrieved successfully'
      };

      res.status(httpStatus.OK).json(response);
    } catch (error: any) {
      console.error('Error getting farms dropdown by seller:', error);
      
      res.status(httpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        error: {
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to get farms dropdown',
          details: error.message
        },
        timestamp: new Date().toISOString()
      });
    }
  };

  /**
   * Get crops dropdown for authenticated seller
   */
  getCropsDropdown = async (req: Request, res: Response): Promise<void> => {
    try {
      // Extract seller ID from authenticated user
      const sellerId = req.user?.id;
      
      if (!sellerId) {
        res.status(httpStatus.UNAUTHORIZED).json({
          success: false,
          error: {
            code: 'UNAUTHORIZED',
            message: 'User not authenticated'
          },
          timestamp: new Date().toISOString()
        });
        return;
      }

      // Extract query parameters
      const queryDto: DropdownQueryDto = {
        status: req.query.status as string,
        search: req.query.search as string,
        limit: req.query.limit ? parseInt(req.query.limit as string, 10) : 50
      };

      const { crops, total } = await this.dropdownService.getCropsDropdown(sellerId, queryDto);

      const response: DropdownResponseDto<any> = {
        success: true,
        data: crops,
        total,
        message: 'Crops dropdown retrieved successfully'
      };

      res.status(httpStatus.OK).json(response);
    } catch (error: any) {
      console.error('Error getting crops dropdown:', error);
      
      res.status(httpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        error: {
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to get crops dropdown',
          details: error.message
        },
        timestamp: new Date().toISOString()
      });
    }
  };

  /**
   * Get enum dropdown values
   */
  getEnumDropdown = async (req: Request, res: Response): Promise<void> => {
    try {
      const { category, subcategory } = req.params;

      if (!category || !subcategory) {
        res.status(httpStatus.BAD_REQUEST).json({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Category and subcategory are required'
          },
          timestamp: new Date().toISOString()
        });
        return;
      }

      const enumData = await this.dropdownService.getEnumDropdown(category, subcategory);

      if (!enumData) {
        res.status(httpStatus.NOT_FOUND).json({
          success: false,
          error: {
            code: 'NOT_FOUND',
            message: 'Enum not found for the specified category and subcategory'
          },
          timestamp: new Date().toISOString()
        });
        return;
      }

      const response: DropdownResponseDto<string> = {
        success: true,
        data: enumData.values,
        total: enumData.values.length,
        message: 'Enum dropdown retrieved successfully'
      };

      res.status(httpStatus.OK).json(response);
    } catch (error: any) {
      console.error('Error getting enum dropdown:', error);
      
      res.status(httpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        error: {
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to get enum dropdown',
          details: error.message
        },
        timestamp: new Date().toISOString()
      });
    }
  };

  /**
   * Get available enum categories and subcategories
   */
  getAvailableEnums = async (req: Request, res: Response): Promise<void> => {
    try {
      const enums = await this.dropdownService.getAvailableEnums();

      const response: DropdownResponseDto<any> = {
        success: true,
        data: enums,
        total: enums.length,
        message: 'Available enums retrieved successfully'
      };

      res.status(httpStatus.OK).json(response);
    } catch (error: any) {
      console.error('Error getting available enums:', error);
      
      res.status(httpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        error: {
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to get available enums',
          details: error.message
        },
        timestamp: new Date().toISOString()
      });
    }
  };
}
