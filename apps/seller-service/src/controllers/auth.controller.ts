import { Request, Response } from 'express';
import { AuthService } from '../services/auth.service';
import { SellerLoginDto } from '../dto/seller.dto';
import { MongoDBConnector } from '@libs/shared';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Initialize MongoDB connector
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/agritech';
const DB_NAME = process.env.DB_NAME || 'agritech';
const dbConnector = MongoDBConnector.getInstance({
  uri: MONGODB_URI,
  options: { dbName: DB_NAME }
});
const authService = new AuthService(dbConnector);

export class AuthController {
  /**
   * Login a seller
   * @param req Express request
   * @param res Express response
   */
  async login(req: Request, res: Response): Promise<void> {
    try {
      const loginDto: SellerLoginDto = req.body;
      
      // Validate required fields
      if (!loginDto.email || !loginDto.password) {
        res.status(400).json({ message: 'Email and password are required' });
        return;
      }
      
      const { token, seller } = await authService.login(loginDto);
      
      res.status(200).json({
        status: 'success',
        data: {
          token,
          seller,
        },
      });
    } catch (error) {
      console.error('Error in login controller:', error);
      
      if (error.message === 'Invalid email or password') {
        res.status(401).json({
          status: 'fail',
          message: 'Invalid email or password',
        });
      } else if (error.message === 'Your account is not active. Please contact support.') {
        res.status(403).json({
          status: 'fail',
          message: error.message,
        });
      } else {
        res.status(500).json({
          status: 'error',
          message: 'Internal server error',
        });
      }
    }
  }
} 