import { Request, Response } from 'express';
import { SellerOnboardingService } from '../services/seller-onboarding.service';
import { RegisterSellerDto, SellerDocumentsDto, UpdateVerificationStatusDto } from '../dto/seller.dto';
import { MongoDBConnector } from '@libs/shared';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Initialize MongoDB connector
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/agritech';
const DB_NAME = process.env.DB_NAME || 'agritech';
const dbConnector = MongoDBConnector.getInstance({
  uri: MONGODB_URI,
  options: { dbName: DB_NAME }
});
const sellerOnboardingService = new SellerOnboardingService(dbConnector);

export class SellerOnboardingController {
  /**
   * Register a new seller
   * @param req Express request
   * @param res Express response
   */
  async registerSeller(req: Request, res: Response): Promise<void> {
    try {
      const registerDto: RegisterSellerDto = req.body;
      
      // Validate required fields
      if (!registerDto.name || !registerDto.email || !registerDto.contact || !registerDto.address || !registerDto.password) {
        res.status(400).json({ message: 'Missing required fields' });
        return;
      }
      
      const seller = await sellerOnboardingService.registerSeller(registerDto);
      res.status(201).json(seller);
    } catch (error) {
      console.error('Error in registerSeller controller:', error);
      if (error.message === 'Email already registered') {
        res.status(409).json({ message: error.message });
      } else {
        res.status(500).json({ message: 'Internal server error' });
      }
    }
  }
  
  /**
   * Upload seller documents
   * @param req Express request
   * @param res Express response
   */
  async uploadDocuments(req: Request, res: Response): Promise<void> {
    try {
      const sellerId = req.params.sellerId;
      // If file is uploaded via multer
      if (req.file) {
        // Build the file URL (assuming static serving from /assets/kyc/)
        const fileUrl = `/assets/kyc/${req.file.filename}`;
        // Optionally, get documentType from req.body
        const documentType = req.body.documentType || 'identityProof';
        // Build the documentsDto accordingly
        const documentsDto: SellerDocumentsDto = {
          identityProof: documentType === 'identityProof' ? fileUrl : '',
          landOwnership: documentType === 'landOwnership' ? fileUrl : '',
          certifications: documentType === 'certification' ? [fileUrl] : [],
        };
        const updatedSeller = await sellerOnboardingService.uploadDocuments(sellerId, documentsDto);
        if (!updatedSeller) {
          res.status(404).json({ message: 'Seller not found' });
          return;
        }
        res.status(200).json({
          message: 'File uploaded successfully',
          fileUrl,
          seller: updatedSeller,
        });
        return;
      }
      // Legacy: Accept document URLs in body
      const documentsDto: SellerDocumentsDto = req.body;
      if (!documentsDto.identityProof || !documentsDto.landOwnership) {
        res.status(400).json({ message: 'Missing required document files' });
        return;
      }
      const updatedSeller = await sellerOnboardingService.uploadDocuments(sellerId, documentsDto);
      if (!updatedSeller) {
        res.status(404).json({ message: 'Seller not found' });
        return;
      }
      res.status(200).json(updatedSeller);
    } catch (error) {
      console.error('Error in uploadDocuments controller:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  }
  
  /**
   * Update seller verification status
   * @param req Express request
   * @param res Express response
   */
  async updateVerificationStatus(req: Request, res: Response): Promise<void> {
    try {
      const statusDto: UpdateVerificationStatusDto = req.body;
      
      // Validate required fields
      if (!statusDto.sellerId || !statusDto.status) {
        res.status(400).json({ message: 'Missing required fields' });
        return;
      }
      
      const updatedSeller = await sellerOnboardingService.updateVerificationStatus(statusDto);
      
      if (!updatedSeller) {
        res.status(404).json({ message: 'Seller not found' });
        return;
      }
      
      res.status(200).json(updatedSeller);
    } catch (error) {
      console.error('Error in updateVerificationStatus controller:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  }
  
  /**
   * Activate seller account
   * @param req Express request
   * @param res Express response
   */
  async activateAccount(req: Request, res: Response): Promise<void> {
    try {
      const sellerId = req.params.sellerId;
      
      const activatedSeller = await sellerOnboardingService.activateAccount(sellerId);
      
      if (!activatedSeller) {
        res.status(404).json({ message: 'Seller not found' });
        return;
      }
      
      res.status(200).json(activatedSeller);
    } catch (error) {
      console.error('Error in activateAccount controller:', error);
      if (error.message === 'Seller must be verified before activation') {
        res.status(400).json({ message: error.message });
      } else {
        res.status(500).json({ message: 'Internal server error' });
      }
    }
  }
  
  /**
   * Perform background check
   * @param req Express request
   * @param res Express response
   */
  async performBackgroundCheck(req: Request, res: Response): Promise<void> {
    try {
      const sellerId = req.params.sellerId;
      
      const result = await sellerOnboardingService.performBackgroundCheck(sellerId);
      
      res.status(200).json(result);
    } catch (error) {
      console.error('Error in performBackgroundCheck controller:', error);
      if (error.message === 'Seller not found') {
        res.status(404).json({ message: error.message });
      } else {
        res.status(500).json({ message: 'Internal server error' });
      }
    }
  }
} 