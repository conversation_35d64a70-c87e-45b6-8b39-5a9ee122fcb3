import { Request, Response } from 'express';
import { SellerProfileService } from '../services/seller-profile.service';
import { SellerBankDetailsDto, UpdateSellerProfileDto } from '../dto/seller.dto';
import { dbManager } from '@libs/shared';

// Get MongoDB connector from the database manager
const mongoConnector = dbManager.getMongoDBConnector();
// Initialize seller profile service with the connector
const sellerProfileService = new SellerProfileService(mongoConnector);

export class SellerProfileController {
  /**
   * Get seller profile
   * @param req Express request
   * @param res Express response
   */
  async getProfile(req: Request, res: Response): Promise<void> {
    try {
      // Get seller ID from the authenticated user's token instead of URL params
      if (!req.user || !req.user.id) {
        res.status(401).json({ message: 'Authentication required' });
        return;
      }

      const sellerId = req.user.id;

      const seller = await sellerProfileService.getProfile(sellerId);

      if (!seller) {
        res.status(404).json({ message: 'Seller not found' });
        return;
      }

      res.status(200).json(seller);
    } catch (error) {
      console.error('Error in getProfile controller:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  }
  
  /**
   * Update seller profile
   * @param req Express request
   * @param res Express response
   */
  async updateProfile(req: Request, res: Response): Promise<void> {
    try {
      // Get seller ID from the authenticated user's token instead of URL params
      if (!req.user || !req.user.id) {
        res.status(401).json({ message: 'Authentication required' });
        return;
      }

      const sellerId = req.user.id;
      const profileDto: UpdateSellerProfileDto = req.body;

      const updatedSeller = await sellerProfileService.updateProfile(sellerId, profileDto);

      if (!updatedSeller) {
        res.status(404).json({ message: 'Seller not found' });
        return;
      }

      res.status(200).json(updatedSeller);
    } catch (error) {
      console.error('Error in updateProfile controller:', error);
      if (error.message === 'Email already in use by another seller') {
        res.status(409).json({ message: error.message });
      } else {
        res.status(500).json({ message: 'Internal server error' });
      }
    }
  }
  
  /**
   * Update seller bank details
   * @param req Express request
   * @param res Express response
   */
  async updateBankDetails(req: Request, res: Response): Promise<void> {
    try {
      // Get seller ID from the authenticated user's token instead of URL params
      if (!req.user || !req.user.id) {
        res.status(401).json({ message: 'Authentication required' });
        return;
      }

      const sellerId = req.user.id;
      const bankDetailsDto: SellerBankDetailsDto = req.body;

      // Validate required fields
      if (!bankDetailsDto.accountNumber || !bankDetailsDto.bankName || !bankDetailsDto.ifscCode) {
        res.status(400).json({ message: 'Missing required fields' });
        return;
      }

      const updatedSeller = await sellerProfileService.updateBankDetails(sellerId, bankDetailsDto);

      if (!updatedSeller) {
        res.status(404).json({ message: 'Seller not found' });
        return;
      }

      res.status(200).json(updatedSeller);
    } catch (error) {
      console.error('Error in updateBankDetails controller:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  }
  
  /**
   * Get all sellers
   * @param req Express request
   * @param res Express response
   */
  async getAllSellers(req: Request, res: Response): Promise<void> {
    try {
      const page = req.query.page ? parseInt(req.query.page as string, 10) : 1;
      const limit = req.query.limit ? parseInt(req.query.limit as string, 10) : 10;
      
      const sellersData = await sellerProfileService.getAllSellers(page, limit);
      
      res.status(200).json(sellersData);
    } catch (error) {
      console.error('Error in getAllSellers controller:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  }
  
  /**
   * Delete seller account
   * @param req Express request
   * @param res Express response
   */
  async deleteAccount(req: Request, res: Response): Promise<void> {
    try {
      // Get seller ID from the authenticated user's token instead of URL params
      if (!req.user || !req.user.id) {
        res.status(401).json({ message: 'Authentication required' });
        return;
      }

      const sellerId = req.user.id;

      const deleted = await sellerProfileService.deleteAccount(sellerId);

      if (!deleted) {
        res.status(404).json({ message: 'Seller not found' });
        return;
      }

      res.status(204).send();
    } catch (error) {
      console.error('Error in deleteAccount controller:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  }
} 