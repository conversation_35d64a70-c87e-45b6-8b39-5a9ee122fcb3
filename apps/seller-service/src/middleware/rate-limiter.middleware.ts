import { 
  createStandardLimiter, 
  createAuthLimiter, 
  createRegistrationLimiter 
} from '@libs/shared';

/**
 * Create a standard rate limiter for most API endpoints
 * Allows 100 requests per IP per 15 minutes
 */
export const standardLimiter = createStandardLimiter();

/**
 * Create a strict rate limiter for authentication endpoints
 * Allows 5 requests per IP per 15 minutes
 * This helps prevent brute force attacks
 */
export const authLimiter = createAuthLimiter();

/**
 * Create a registration rate limiter
 * Allows 3 registrations per IP per day
 * This helps prevent automated registrations
 */
export const registrationLimiter = createRegistrationLimiter(); 