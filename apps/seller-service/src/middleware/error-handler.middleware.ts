import { createErrorHandler, AppError } from '@libs/shared';
export { createError } from '@libs/shared'; 

// Create error handler with development configuration
const errorHandler = createErrorHandler({
  showStackTrace: process.env.NODE_ENV === 'development',
  defaultMessage: 'Something went wrong',
});

// Export the error handler and AppError interface
export { errorHandler, AppError };

// Re-export the createError function for convenience