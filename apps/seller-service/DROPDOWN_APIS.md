# Dropdown APIs Documentation

This document describes the dropdown listing APIs implemented in the seller service for providing simplified data suitable for dropdown components in frontend applications.

## Overview

The dropdown APIs provide lightweight endpoints that return minimal data optimized for dropdown selections. These APIs are designed to:

- Return only essential fields (ID, name, status)
- Support filtering and search functionality
- Implement proper authentication and authorization
- Provide consistent response formats

## Authentication

All dropdown endpoints require authentication via JWT token:
```
Authorization: Bearer <jwt_token>
```

## API Endpoints

### 1. Get Farms Dropdown

**Endpoint:** `GET /api/v1/sellers/dropdowns/farms`

**Description:** Returns a list of farms for the authenticated seller in dropdown format.

**Query Parameters:**
- `status` (optional): Filter by farm status (`ACTIVE` or `INACTIVE`)
- `search` (optional): Search by farm name, city, or state
- `limit` (optional): Maximum number of results (default: 50)

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "farmId": "FARM-001",
      "name": "Green Valley Farm",
      "status": "ACTIVE",
      "location": {
        "city": "Narsapur",
        "state": "Andhra Pradesh"
      }
    }
  ],
  "total": 1,
  "message": "Farms dropdown retrieved successfully"
}
```

### 2. Get Farms Dropdown by Seller ID (Admin Only)

**Endpoint:** `GET /api/v1/sellers/dropdowns/farms/:sellerId`

**Description:** Returns farms dropdown for a specific seller. Requires admin role or the seller must be requesting their own data.

**Path Parameters:**
- `sellerId`: ID of the seller

**Query Parameters:** Same as above

**Authorization:** Admin role or seller accessing their own data

### 3. Get Crops Dropdown

**Endpoint:** `GET /api/v1/sellers/dropdowns/crops`

**Description:** Returns a list of crops for the authenticated seller in dropdown format.

**Query Parameters:**
- `status` (optional): Filter by crop health status
- `search` (optional): Search by crop name or type
- `limit` (optional): Maximum number of results (default: 50)

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "cropId": "CROP-001",
      "name": "Organic Tomatoes",
      "category": "Vegetables",
      "status": "HEALTHY",
      "farmName": "FARM-001"
    }
  ],
  "total": 1,
  "message": "Crops dropdown retrieved successfully"
}
```

### 4. Get Enum Dropdown Values

**Endpoint:** `GET /api/v1/sellers/dropdowns/enums/:category/:subcategory`

**Description:** Returns enum values for a specific category and subcategory.

**Path Parameters:**
- `category`: Enum category (e.g., `CROP`, `FARMER`, `LOCATION`)
- `subcategory`: Enum subcategory (e.g., `crop_category`, `farming_method`)

**Response:**
```json
{
  "success": true,
  "data": [
    "Vegetables",
    "Fruits",
    "Paddy/Rice",
    "Wheat",
    "Pulses"
  ],
  "total": 5,
  "message": "Enum dropdown retrieved successfully"
}
```

### 5. Get Available Enums

**Endpoint:** `GET /api/v1/sellers/dropdowns/enums`

**Description:** Returns all available enum categories and subcategories.

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "category": "CROP",
      "subcategory": "crop_category",
      "name": "Crop Categories"
    },
    {
      "category": "CROP",
      "subcategory": "farming_method",
      "name": "Farming Methods"
    }
  ],
  "total": 2,
  "message": "Available enums retrieved successfully"
}
```

## Error Responses

All endpoints return consistent error responses:

```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "Error description",
    "details": "Additional error details"
  },
  "timestamp": "2025-06-16T18:30:00.000Z"
}
```

### Common Error Codes:
- `UNAUTHORIZED`: User not authenticated
- `FORBIDDEN`: Access denied (admin role required)
- `VALIDATION_ERROR`: Invalid request parameters
- `NOT_FOUND`: Resource not found
- `INTERNAL_SERVER_ERROR`: Server error

## Rate Limiting

All dropdown endpoints use standard rate limiting:
- 100 requests per 15 minutes per IP address

## Usage Examples

### Frontend Integration

```javascript
// Get farms dropdown for authenticated seller
const farmsResponse = await fetch('/api/v1/sellers/dropdowns/farms?status=ACTIVE', {
  headers: {
    'Authorization': `Bearer ${token}`
  }
});
const farms = await farmsResponse.json();

// Populate dropdown
const farmSelect = document.getElementById('farmSelect');
farms.data.forEach(farm => {
  const option = document.createElement('option');
  option.value = farm.farmId;
  option.textContent = `${farm.name} (${farm.location.city})`;
  farmSelect.appendChild(option);
});
```

### React Component Example

```jsx
import { useState, useEffect } from 'react';

const FarmDropdown = ({ onSelect }) => {
  const [farms, setFarms] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchFarms = async () => {
      try {
        const response = await fetch('/api/v1/sellers/dropdowns/farms', {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          }
        });
        const data = await response.json();
        setFarms(data.data);
      } catch (error) {
        console.error('Error fetching farms:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchFarms();
  }, []);

  if (loading) return <div>Loading farms...</div>;

  return (
    <select onChange={(e) => onSelect(e.target.value)}>
      <option value="">Select a farm</option>
      {farms.map(farm => (
        <option key={farm.farmId} value={farm.farmId}>
          {farm.name} - {farm.location.city}, {farm.location.state}
        </option>
      ))}
    </select>
  );
};
```

## Testing with Postman

The dropdown endpoints have been added to the seller-service Postman collection under the "Dropdown APIs" folder. Import the updated collection and:

1. First authenticate using "Seller Login" endpoint
2. Use the dropdown endpoints with the automatically saved JWT token
3. Test different query parameters and scenarios

## Implementation Details

### Files Created/Modified:
- `apps/seller-service/src/dto/dropdown.dto.ts` - Data transfer objects
- `apps/seller-service/src/services/dropdown.service.ts` - Business logic
- `apps/seller-service/src/controllers/dropdown.controller.ts` - HTTP handlers
- `apps/seller-service/src/routes/seller.routes.ts` - Route definitions
- `libs/shared/database-connectors/services/enum.service.ts` - Added getAllEnums method
- `postman/seller-service.json` - Updated Postman collection

### Key Features:
- **Authentication-based filtering**: Automatically filters data by authenticated seller ID
- **Admin access**: Allows admin users to access any seller's data
- **Search functionality**: Supports text search across relevant fields
- **Consistent response format**: All endpoints return standardized response structure
- **Error handling**: Comprehensive error handling with proper HTTP status codes
- **Rate limiting**: Standard rate limiting applied to all endpoints
