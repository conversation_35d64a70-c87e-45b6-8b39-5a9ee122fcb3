import { v4 as uuidv4 } from 'uuid';
import { Client } from '@elastic/elasticsearch';
import mongoose from 'mongoose';
import { CreateFarmDto, UpdateFarmDto, QueryFarmsDto, FarmAnalyticsDto, MinimalFarmResponseDto } from '../dto/farm.dto';
import { FarmModel, FarmDocument as MongoFarmDocument } from '@libs/shared';
import { FarmDocument } from '@libs/shared';

// Type for Elasticsearch documents
type ElasticFarmDocument = {
  id: string;
  farmId: string;
  sellerId: string;
  location: any;
  totalArea: number;
  soilType: string;
  waterSource: string;
  infrastructure: string[];
  certifications: string[];
  status: string;
  createdAt: Date;
  updatedAt: Date;
};

/**
 * Service class for farm operations
 */
export class FarmService {
  private esClient: Client;
  private farmModel: mongoose.Model<MongoFarmDocument>;
  private readonly INDEX_NAME = process.env.ELASTICSEARCH_INDEX || 'befarma';

  /**
   * Constructor for FarmService
   * @param esClient - Elasticsearch client
   */
  constructor(esClient: Client) {
    this.esClient = esClient;
    this.farmModel = FarmModel;
  }

  /**
   * Create a new farm
   * @param createFarmDto - Data to create a farm
   * @returns The created farm
   */
  async createFarm(createFarmDto: CreateFarmDto): Promise<MongoFarmDocument> {
    try {
      // Generate a unique farmId
      const farmId = `farm-${uuidv4()}`;
      
      // Prepare farm data with defaults for MongoDB
      const farmData = {
        ...createFarmDto,
        farmId,
        status: 'ACTIVE',
        plots: []
      };
      
      // Create the farm in MongoDB
      const farm = new this.farmModel(farmData);
      const savedFarm = await farm.save();
      
      // Index the farm in Elasticsearch
      await this.indexFarmInElasticsearch(savedFarm);
      
      return savedFarm;
    } catch (error) {
      console.error('Error creating farm:', error);
      throw error;
    }
  }

  /**
   * Update a farm by ID
   * @param farmId - ID of the farm to update
   * @param updateFarmDto - Data to update the farm
   * @returns The updated farm
   */
  async updateFarm(farmId: string, updateFarmDto: UpdateFarmDto): Promise<MongoFarmDocument | null> {
    try {
      // Update the farm in MongoDB
      const updatedFarm = await this.farmModel.findOneAndUpdate(
        { farmId },
        { $set: updateFarmDto },
        { new: true }
      );
      
      if (!updatedFarm) {
        return null;
      }
      
      // Update the farm in Elasticsearch
      await this.updateFarmInElasticsearch(updatedFarm);
      
      return updatedFarm;
    } catch (error) {
      console.error('Error updating farm:', error);
      throw error;
    }
  }

  /**
   * Delete a farm by ID
   * @param farmId - ID of the farm to delete
   * @returns Boolean indicating success
   */
  async deleteFarm(farmId: string): Promise<boolean> {
    try {
      // Delete the farm from MongoDB
      const deletionResult = await this.farmModel.deleteOne({ farmId });
      
      if (deletionResult.deletedCount === 0) {
        return false;
      }
      
      // Delete the farm from Elasticsearch
      await this.esClient.delete({
        index: this.INDEX_NAME,
        id: farmId
      });
      
      return true;
    } catch (error) {
      console.error('Error deleting farm:', error);
      throw error;
    }
  }

  /**
   * Get a farm by ID
   * @param farmId - ID of the farm to retrieve
   * @returns The farm data or null
   */
  async getFarmById(farmId: string): Promise<MongoFarmDocument | null> {
    try {
      return await this.farmModel.findOne({ farmId });
    } catch (error) {
      console.error('Error getting farm by ID:', error);
      throw error;
    }
  }

  /**
   * Query farms with filters
   * @param queryDto - Query parameters
   * @returns Array of farms and total count
   */
  async queryFarms(queryDto: QueryFarmsDto): Promise<{ farms: MongoFarmDocument[]; total: number }> {
    try {
      const {
        sellerId,
        status,
        waterSource,
        soilType,
        state,
        city,
        page = 1,
        limit = 10,
        sortBy = 'createdAt',
        sortOrder = 'desc'
      } = queryDto;
      
      // Build the query filter
      const filter: any = {};
      
      if (sellerId) filter.sellerId = sellerId;
      if (status) filter.status = status;
      if (waterSource) filter.waterSource = waterSource;
      if (soilType) filter.soilType = soilType;
      if (state) filter['location.state'] = state;
      if (city) filter['location.city'] = city;
      
      // Calculate pagination
      const skip = (page - 1) * limit;
      
      // Prepare sort options
      const sort: any = {};
      sort[sortBy] = sortOrder === 'asc' ? 1 : -1;
      
      // Execute query with pagination
      const farms = await this.farmModel
        .find(filter)
        .sort(sort)
        .skip(skip)
        .limit(limit);
      
      // Get total count for pagination
      const total = await this.farmModel.countDocuments(filter);
      
      return { farms, total };
    } catch (error) {
      console.error('Error querying farms:', error);
      throw error;
    }
  }

  /**
   * Search farms using Elasticsearch
   * @param query - Search query
   * @param filters - Additional filters
   * @param page - Page number
   * @param limit - Results per page
   * @returns Search results
   */
  async searchFarms(
    query: string,
    filters: any = {},
    page: number = 1,
    limit: number = 10
  ): Promise<{ farms: any[]; total: number }> {
    try {
      const from = (page - 1) * limit;
      
      // Build Elasticsearch query
      const esQuery: any = {
        bool: {
          must: [
            {
              multi_match: {
                query,
                fields: ['name^2', 'soilType', 'waterSource', 'location.city', 'location.state']
              }
            }
          ],
          filter: []
        }
      };
      
      // Add filters
      if (filters.sellerId) {
        esQuery.bool.filter.push({ term: { sellerId: filters.sellerId } });
      }
      
      if (filters.status) {
        esQuery.bool.filter.push({ term: { status: filters.status } });
      }
      
      if (filters.waterSource) {
        esQuery.bool.filter.push({ term: { waterSource: filters.waterSource } });
      }
      
      if (filters.soilType) {
        esQuery.bool.filter.push({ term: { soilType: filters.soilType } });
      }
      
      if (filters.state) {
        esQuery.bool.filter.push({ term: { 'location.state': filters.state } });
      }
      
      if (filters.city) {
        esQuery.bool.filter.push({ term: { 'location.city': filters.city } });
      }
      
      // Execute search
      const searchParams = {
        index: this.INDEX_NAME,
        query: esQuery,
        from,
        size: limit,
        sort: [
          { _score: { order: 'desc' as 'desc' } },
          { createdAt: { order: 'desc' as 'desc' } }
        ]
      };
      
      const result = await this.esClient.search(searchParams);
      
      const hits = result.hits.hits;
      const total = result.hits.total as any;
      
      // Extract results
      const farms = hits.map((hit: any) => ({
        ...hit._source,
        score: hit._score
      }));
      
      return {
        farms,
        total: total.value || 0
      };
    } catch (error) {
      console.error('Error searching farms:', error);
      throw error;
    }
  }

  /**
   * Get farm analytics
   * @param analyticsDto - Analytics query parameters
   * @returns Analytics data
   */
  async getFarmAnalytics(analyticsDto: FarmAnalyticsDto): Promise<any> {
    try {
      const { sellerId, fromDate, toDate } = analyticsDto;
      
      // Build filter
      const filter: any = { sellerId };
      
      // Date filter
      if (fromDate || toDate) {
        filter.createdAt = {};
        if (fromDate) filter.createdAt.$gte = fromDate;
        if (toDate) filter.createdAt.$lte = toDate;
      }
      
      // Get total farms count
      const totalFarms = await this.farmModel.countDocuments(filter);
      
      // Get total area across all farms
      const areaResult = await this.farmModel.aggregate([
        { $match: filter },
        { $group: { _id: null, totalArea: { $sum: '$totalArea' } } }
      ]);
      
      const totalArea = areaResult.length > 0 ? areaResult[0].totalArea : 0;
      
      // Get farms by status
      const statusCounts = await this.farmModel.aggregate([
        { $match: filter },
        { $group: { _id: '$status', count: { $sum: 1 } } }
      ]);
      
      const activeFarms = statusCounts.find(s => s._id === 'ACTIVE')?.count || 0;
      const inactiveFarms = statusCounts.find(s => s._id === 'INACTIVE')?.count || 0;
      
      // Get water source distribution
      const waterSourceDistribution = await this.farmModel.aggregate([
        { $match: filter },
        { $group: { _id: '$waterSource', count: { $sum: 1 } } },
        { $project: { _id: 0, source: '$_id', count: 1 } }
      ]);
      
      // Get soil type distribution
      const soilTypeDistribution = await this.farmModel.aggregate([
        { $match: filter },
        { $group: { _id: '$soilType', count: { $sum: 1 } } },
        { $project: { _id: 0, type: '$_id', count: 1 } }
      ]);
      
      // Format and return analytics
      return {
        totalFarms,
        totalArea,
        activeFarms,
        inactiveFarms,
        waterSourceDistribution: waterSourceDistribution.reduce((acc: any, curr: any) => {
          acc[curr.source] = curr.count;
          return acc;
        }, {}),
        soilTypeDistribution: soilTypeDistribution.reduce((acc: any, curr: any) => {
          acc[curr.type] = curr.count;
          return acc;
        }, {})
      };
    } catch (error) {
      console.error('Error getting farm analytics:', error);
      throw error;
    }
  }

  /**
   * Index a farm in Elasticsearch
   * @param farm - Farm to index
   */
  private async indexFarmInElasticsearch(farm: MongoFarmDocument): Promise<void> {
    try {
      const document: ElasticFarmDocument = this.convertToElasticsearchDocument(farm);
      
      const params = {
        index: this.INDEX_NAME,
        id: farm.farmId,
        document,
        refresh: true
      };
      
      await this.esClient.index(params);
    } catch (error) {
      console.error('Error indexing farm in Elasticsearch:', error);
      throw error;
    }
  }

  /**
   * Update a farm in Elasticsearch
   * @param farm - Updated farm data
   */
  private async updateFarmInElasticsearch(farm: MongoFarmDocument): Promise<void> {
    try {
      const document: ElasticFarmDocument = this.convertToElasticsearchDocument(farm);
      
      const params = {
        index: this.INDEX_NAME,
        id: farm.farmId,
        doc: document,
        refresh: true
      };
      
      await this.esClient.update(params);
    } catch (error) {
      console.error('Error updating farm in Elasticsearch:', error);
      throw error;
    }
  }

  /**
   * Convert MongoDB farm document to Elasticsearch document
   * @param farm - MongoDB farm document
   * @returns Elasticsearch farm document
   */
  private convertToElasticsearchDocument(farm: MongoFarmDocument): ElasticFarmDocument {
    return {
      id: farm._id.toString(),
      farmId: farm.farmId,
      sellerId: farm.sellerId,
      location: farm.location,
      totalArea: farm.totalArea,
      soilType: farm.soilType,
      waterSource: farm.waterSource,
      infrastructure: farm.infrastructure,
      certifications: farm.certifications,
      status: farm.status,
      createdAt: farm.createdAt,
      updatedAt: farm.updatedAt
    };
  }

  /**
   * Reindex all farms from MongoDB to Elasticsearch
   */
  async reindexAllFarms(): Promise<void> {
    try {
      // Get all farms from MongoDB
      const farms = await this.farmModel.find();
      
      // Create index with mapping if it doesn't exist
      const indexExists = await this.esClient.indices.exists({ index: this.INDEX_NAME });
      
      if (!indexExists) {
        const indexParams = {
          index: this.INDEX_NAME,
          settings: {
            number_of_shards: 3,
            number_of_replicas: 1,
            analysis: {
              analyzer: {
                farm_analyzer: {
                  type: 'custom' as 'custom',
                  tokenizer: 'standard',
                  filter: ['lowercase', 'asciifolding']
                }
              }
            }
          }
        };
        
        await this.esClient.indices.create(indexParams);
      }
      
      // Bulk index all farms
      if (farms.length > 0) {
        const operations = farms.flatMap(farm => [
          { index: { _index: this.INDEX_NAME, _id: farm.farmId } },
          this.convertToElasticsearchDocument(farm)
        ]);
        
        const bulkParams = {
          refresh: true,
          operations
        };
        
        await this.esClient.bulk(bulkParams);
        
        console.log(`Successfully reindexed ${farms.length} farms`);
      }
    } catch (error) {
      console.error('Error reindexing farms:', error);
      throw error;
    }
  }

  /**
   * Get farms by seller ID
   * @param sellerId - ID of the seller to retrieve farms for
   * @returns Array of farms for the seller
   */
  async getFarmsBySellerId(sellerId: string): Promise<MongoFarmDocument[]> {
    try {
      return await this.farmModel.find({ sellerId });
    } catch (error) {
      console.error('Error getting farms by seller ID:', error);
      throw error;
    }
  }

  /**
   * Get minimal farm details for a seller (used in dropdowns)
   * @param sellerId - ID of the seller
   * @returns Array of minimal farm details
   */
  async getMinimalFarmsBySellerId(sellerId: string): Promise<MinimalFarmResponseDto[]> {
    try {
      const farms = await this.farmModel
        .find({ sellerId, status: 'ACTIVE' })
        .select('farmId name location.state location.city status')
        .lean();

      return farms.map(farm => ({
        farmId: farm.farmId,
        name: farm.name,
        location: {
          state: farm.location.state,
          city: farm.location.city
        },
        status: farm.status
      }));
    } catch (error) {
      console.error('Error getting minimal farms by seller ID:', error);
      throw error;
    }
  }
}
