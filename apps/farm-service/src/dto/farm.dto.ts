/**
 * Data Transfer Objects for Farm Service
 */

/**
 * Location DTO
 */
export class LocationDto {
  country: string;
  state: string;
  city: string;
  pincode: string;
  addressLine1: string;
  addressLine2: string;
  coordinates?: {
    latitude: number;
    longitude: number;
  };
}

/**
 * DTO for creating a new farm
 */
export class CreateFarmDto {
  sellerId: string;
  name: string;
  location: LocationDto;
  totalArea: number;
  soilType: string;
  waterSource: string;
  infrastructure?: string[];
  certifications?: string[];
}

/**
 * DTO for updating an existing farm
 */
export class UpdateFarmDto {
  name?: string;
  location?: LocationDto;
  totalArea?: number;
  soilType?: string;
  waterSource?: string;
  infrastructure?: string[];
  certifications?: string[];
  status?: 'ACTIVE' | 'INACTIVE';
}

/**
 * DTO for querying farms
 */
export class QueryFarmsDto {
  sellerId?: string;
  status?: 'ACTIVE' | 'INACTIVE';
  waterSource?: string;
  soilType?: string;
  state?: string;
  city?: string;
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

/**
 * DTO for farm analytics
 */
export class FarmAnalyticsDto {
  sellerId: string;
  fromDate?: Date;
  toDate?: Date;
}

// Farm Response DTO
export class FarmResponseDto {
  farmId: string;
  sellerId: string;
  location: {
    country: string;
    state: string;
    city: string;
    pincode: string;
    addressLine1: string;
    addressLine2: string;
    coordinates?: {
      latitude: number;
      longitude: number;
    };
  };
  totalArea: number;
  soilType: string;
  waterSource: string;
  infrastructure: string[];
  certifications: string[];
  status: 'ACTIVE' | 'INACTIVE';
  plots: string[];
  createdAt: Date;
  updatedAt: Date;
}

// Farm Filter DTO
export class FarmFilterDto {
  sellerId?: string;
  status?: 'ACTIVE' | 'INACTIVE';
  page?: number;
  limit?: number;
}

/**
 * DTO for minimal farm details (used in dropdowns)
 */
export class MinimalFarmResponseDto {
  farmId: string;
  name: string;
  location: {
    state: string;
    city: string;
  };
  status: 'ACTIVE' | 'INACTIVE';
} 