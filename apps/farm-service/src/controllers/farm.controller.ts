import { Request, Response } from 'express';
import { FarmService } from '../services/farm.service';
import { CreateFarmDto, UpdateFarmDto, QueryFarmsDto, FarmAnalyticsDto } from '../dto/farm.dto';
import httpStatus from 'http-status';

/**
 * Controller for farm-related endpoints
 */
export class FarmController {
  private farmService: FarmService;

  constructor(farmService: FarmService) {
    this.farmService = farmService;
  }

  /**
   * Create a new farm
   * @param req Request object
   * @param res Response object
   */
  createFarm = async (req: Request, res: Response): Promise<void> => {
    try {
      const createFarmDto = req.body as CreateFarmDto;
      const farm = await this.farmService.createFarm(createFarmDto);
      
      res.status(httpStatus.CREATED).json({
        success: true,
        message: 'Farm created successfully',
        data: farm,
      });
    } catch (error: any) {
      console.error('Error creating farm:', error);
      
      res.status(httpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        message: 'Failed to create farm',
        error: error.message,
      });
    }
  };

  /**
   * Get a farm by ID
   * @param req Request object
   * @param res Response object
   */
  getFarmById = async (req: Request, res: Response): Promise<void> => {
    try {
      const { farmId } = req.params;
      const farm = await this.farmService.getFarmById(farmId);
      
      if (!farm) {
        res.status(httpStatus.NOT_FOUND).json({
          success: false,
          message: `Farm with ID ${farmId} not found`,
        });
        return;
      }
      
      res.status(httpStatus.OK).json({
        success: true,
        data: farm,
      });
    } catch (error: any) {
      console.error('Error getting farm:', error);
      
      res.status(httpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        message: 'Failed to get farm',
        error: error.message,
      });
    }
  };

  /**
   * Get farms by seller ID
   * @param req Request object
   * @param res Response object
   */
  getFarmsBySellerId = async (req: Request, res: Response): Promise<void> => {
    try {
      const { sellerId } = req.params;
      const farms = await this.farmService.getFarmsBySellerId(sellerId);
      
      res.status(httpStatus.OK).json({
        success: true,
        data: farms,
      });
    } catch (error) {
      console.error('Error in getFarmsBySellerId controller:', error);
      res.status(httpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        message: 'Failed to get farms',
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  };

  /**
   * Update a farm by ID
   * @param req Request object
   * @param res Response object
   */
  updateFarm = async (req: Request, res: Response): Promise<void> => {
    try {
      const { farmId } = req.params;
      const updateFarmDto = req.body as UpdateFarmDto;
      
      const updatedFarm = await this.farmService.updateFarm(farmId, updateFarmDto);
      
      if (!updatedFarm) {
        res.status(httpStatus.NOT_FOUND).json({
          success: false,
          message: `Farm with ID ${farmId} not found`,
        });
        return;
      }
      
      res.status(httpStatus.OK).json({
        success: true,
        message: 'Farm updated successfully',
        data: updatedFarm,
      });
    } catch (error: any) {
      console.error('Error updating farm:', error);
      
      res.status(httpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        message: 'Failed to update farm',
        error: error.message,
      });
    }
  };

  /**
   * Delete a farm by ID
   * @param req Request object
   * @param res Response object
   */
  deleteFarm = async (req: Request, res: Response): Promise<void> => {
    try {
      const { farmId } = req.params;
      const deleted = await this.farmService.deleteFarm(farmId);
      
      if (!deleted) {
        res.status(httpStatus.NOT_FOUND).json({
          success: false,
          message: `Farm with ID ${farmId} not found`,
        });
        return;
      }
      
      res.status(httpStatus.OK).json({
        success: true,
        message: `Farm with ID ${farmId} deleted successfully`,
      });
    } catch (error: any) {
      console.error('Error deleting farm:', error);
      
      res.status(httpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        message: 'Failed to delete farm',
        error: error.message,
      });
    }
  };

  /**
   * Update farm status
   * @param req Request object
   * @param res Response object
   */
  updateFarmStatus = async (req: Request, res: Response): Promise<void> => {
    try {
      const { farmId } = req.params;
      const { status } = req.body;
      
      if (!status || !['ACTIVE', 'INACTIVE'].includes(status)) {
        res.status(httpStatus.BAD_REQUEST).json({
          success: false,
          message: 'Invalid status. Must be ACTIVE or INACTIVE',
        });
        return;
      }
      
      const updatedFarm = await this.farmService.updateFarm(
        farmId,
        { status: status as 'ACTIVE' | 'INACTIVE' }
      );
      
      if (!updatedFarm) {
        res.status(httpStatus.NOT_FOUND).json({
          success: false,
          message: 'Farm not found',
        });
        return;
      }
      
      res.status(httpStatus.OK).json({
        success: true,
        message: 'Farm status updated successfully',
        data: updatedFarm,
      });
    } catch (error) {
      console.error('Error in updateFarmStatus controller:', error);
      res.status(httpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        message: 'Failed to update farm status',
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  };

  /**
   * Get all farms with filtering and pagination
   * @param req Request object
   * @param res Response object
   */
  getAllFarms = async (req: Request, res: Response): Promise<void> => {
    try {
      const { sellerId, status, page, limit } = req.query;
      
      const filterDto: QueryFarmsDto = {
        sellerId: sellerId as string,
        status: status as 'ACTIVE' | 'INACTIVE',
        page: page ? parseInt(page as string, 10) : undefined,
        limit: limit ? parseInt(limit as string, 10) : undefined,
      };
      
      const { farms, total } = await this.farmService.queryFarms(filterDto);
      const pages = Math.ceil(total / (filterDto.limit || 10));
      
      res.status(httpStatus.OK).json({
        success: true,
        data: farms,
        pagination: {
          total,
          pages,
          currentPage: filterDto.page || 1,
          limit: filterDto.limit || 10,
        },
      });
    } catch (error) {
      console.error('Error in getAllFarms controller:', error);
      res.status(httpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        message: 'Failed to get farms',
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  };

  /**
   * Query farms with filters
   * @param req - Express request
   * @param res - Express response
   */
  queryFarms = async (req: Request, res: Response): Promise<void> => {
    try {
      // Extract query parameters
      const queryDto: QueryFarmsDto = {
        sellerId: req.query.sellerId as string,
        status: req.query.status as 'ACTIVE' | 'INACTIVE',
        waterSource: req.query.waterSource as string,
        soilType: req.query.soilType as string,
        state: req.query.state as string,
        city: req.query.city as string,
        page: req.query.page ? parseInt(req.query.page as string, 10) : 1,
        limit: req.query.limit ? parseInt(req.query.limit as string, 10) : 10,
        sortBy: req.query.sortBy as string,
        sortOrder: req.query.sortOrder as 'asc' | 'desc'
      };
      
      const { farms, total } = await this.farmService.queryFarms(queryDto);
      
      res.status(httpStatus.OK).json({
        success: true,
        data: farms,
        pagination: {
          page: queryDto.page || 1,
          limit: queryDto.limit || 10,
          total
        }
      });
    } catch (error: any) {
      console.error('Error querying farms:', error);
      
      res.status(httpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        message: 'Failed to query farms',
        error: error.message
      });
    }
  };

  /**
   * Search farms using Elasticsearch
   * @param req - Express request
   * @param res - Express response
   */
  searchFarms = async (req: Request, res: Response): Promise<void> => {
    try {
      const { query } = req.query;
      
      if (!query) {
        res.status(httpStatus.BAD_REQUEST).json({
          success: false,
          message: 'Search query is required'
        });
        return;
      }
      
      // Extract filters and pagination
      const filters = {
        sellerId: req.query.sellerId,
        status: req.query.status,
        waterSource: req.query.waterSource,
        soilType: req.query.soilType,
        state: req.query.state,
        city: req.query.city
      };
      
      const page = req.query.page ? parseInt(req.query.page as string, 10) : 1;
      const limit = req.query.limit ? parseInt(req.query.limit as string, 10) : 10;
      
      const { farms, total } = await this.farmService.searchFarms(query as string, filters, page, limit);
      
      res.status(httpStatus.OK).json({
        success: true,
        data: farms,
        pagination: {
          page,
          limit,
          total
        }
      });
    } catch (error: any) {
      console.error('Error searching farms:', error);
      
      res.status(httpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        message: 'Failed to search farms',
        error: error.message
      });
    }
  };

  /**
   * Get farm analytics
   * @param req - Express request
   * @param res - Express response
   */
  getFarmAnalytics = async (req: Request, res: Response): Promise<void> => {
    try {
      const { sellerId } = req.params;
      
      if (!sellerId) {
        res.status(httpStatus.BAD_REQUEST).json({
          success: false,
          message: 'Seller ID is required'
        });
        return;
      }
      
      // Extract analytics parameters
      const analyticsDto: FarmAnalyticsDto = {
        sellerId,
        fromDate: req.query.fromDate ? new Date(req.query.fromDate as string) : undefined,
        toDate: req.query.toDate ? new Date(req.query.toDate as string) : undefined
      };
      
      const analytics = await this.farmService.getFarmAnalytics(analyticsDto);
      
      res.status(httpStatus.OK).json({
        success: true,
        data: analytics
      });
    } catch (error: any) {
      console.error('Error getting farm analytics:', error);
      
      res.status(httpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        message: 'Failed to get farm analytics',
        error: error.message
      });
    }
  };

  /**
   * Reindex all farms
   * @param req - Express request
   * @param res - Express response
   */
  reindexAllFarms = async (req: Request, res: Response): Promise<void> => {
    try {
      await this.farmService.reindexAllFarms();
      
      res.status(httpStatus.OK).json({
        success: true,
        message: 'All farms reindexed successfully'
      });
    } catch (error: any) {
      console.error('Error reindexing farms:', error);
      
      res.status(httpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        message: 'Failed to reindex farms',
        error: error.message
      });
    }
  };

  /**
   * Get minimal farm details for dropdowns
   * @param req - Express request
   * @param res - Express response
   */
  async getMinimalFarmsBySellerId(req: Request, res: Response): Promise<void> {
    try {
      const { sellerId } = req.params;
      const farms = await this.farmService.getMinimalFarmsBySellerId(sellerId);
      
      res.status(httpStatus.OK).json({
        success: true,
        data: farms
      });
    } catch (error) {
      console.error('Error getting minimal farms:', error);
      
      res.status(httpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        message: 'Failed to get minimal farm details',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }
} 
