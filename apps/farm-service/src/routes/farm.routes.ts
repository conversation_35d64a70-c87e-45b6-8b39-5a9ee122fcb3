import express, { Router } from 'express';
import { FarmController } from '../controllers/farm.controller';
import { Client } from '@elastic/elasticsearch';
import { FarmService } from '../services/farm.service';

export function setupFarmRoutes(esClient: Client): Router {
  const router = express.Router();
  const farmService = new FarmService(esClient);
  const farmController = new FarmController(farmService);

  router.post('/', (req, res) => farmController.createFarm(req, res));

  router.get('/:farmId', (req, res) => farmController.getFarmById(req, res));

  router.put('/:farmId', (req, res) => farmController.updateFarm(req, res));

  router.delete('/:farmId', (req, res) => farmController.deleteFarm(req, res));

  router.get('/', (req, res) => farmController.queryFarms(req, res));

  router.get('/search', (req, res) => farmController.searchFarms(req, res));

  router.get('/minimal/:sellerId', (req, res) => farmController.getMinimalFarmsBySellerId(req, res));

  router.get('/analytics/:sellerId', (req, res) => farmController.getFarmAnalytics(req, res));

  router.post('/reindex', (req, res) => farmController.reindexAllFarms(req, res));

  return router;
} 