# Elasticsearch Configuration Guide

## Overview

This guide explains how to configure Elasticsearch for the AgriTech Seller Backend system. The system now supports both Befarma Cloud (recommended) and local Elasticsearch installations.

## Quick Setup

### Option 1: Automated Setup (Recommended)

```bash
# Run the setup script
./setup-elasticsearch.sh

# Test the connection
npm run test:connection
```

### Option 2: Manual Setup

1. Copy the environment template:
```bash
cp .env.example .env
```

2. Edit the `.env` file with your Elasticsearch configuration
3. Test the connection: `npm run test:connection`

## Configuration Options

### Befarma Cloud Configuration (Recommended)

For production use with Befarma Cloud:

```env
# Befarma Elasticsearch Cloud
ELASTICSEARCH_NODE=https://befarma-f44523.es.us-east-1.aws.elastic.cloud:443
ELASTICSEARCH_API_KEY=your_api_key_here
ELASTICSEARCH_INDEX=befarma
ELASTICSEARCH_SERVERLESS=true
```

### Local Elasticsearch Configuration

For development with local Elasticsearch:

```env
# Local Elasticsearch
ELASTICSEARCH_NODE=http://localhost:9200
ELASTICSEARCH_USERNAME=elastic
ELASTICSEARCH_PASSWORD=your_password
ELASTICSEARCH_INDEX=befarma
ELASTICSEARCH_SERVERLESS=false
ELASTICSEARCH_SSL_VERIFY=false
```

### Custom Configuration

For other Elasticsearch providers:

```env
# Custom Elasticsearch
ELASTICSEARCH_NODE=https://your-cluster.example.com:9200
ELASTICSEARCH_API_KEY=your_api_key  # OR username/password
ELASTICSEARCH_INDEX=your_index_name
ELASTICSEARCH_SERVERLESS=false
ELASTICSEARCH_SSL_VERIFY=true
```

## Environment Variables Reference

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `ELASTICSEARCH_NODE` | Elasticsearch cluster URL | `http://localhost:9200` | Yes |
| `ELASTICSEARCH_API_KEY` | API key for authentication | - | No* |
| `ELASTICSEARCH_USERNAME` | Username for basic auth | `elastic` | No* |
| `ELASTICSEARCH_PASSWORD` | Password for basic auth | - | No* |
| `ELASTICSEARCH_INDEX` | Index name for crops/farms | `befarma` | No |
| `ELASTICSEARCH_SERVERLESS` | Enable serverless mode | `false` | No |
| `ELASTICSEARCH_SSL_VERIFY` | Verify SSL certificates | `true` | No |
| `ELASTICSEARCH_MAX_RETRIES` | Max retry attempts | `3` | No |
| `ELASTICSEARCH_REQUEST_TIMEOUT` | Request timeout (ms) | `30000` | No |

*Either API key OR username/password is required for authentication.

## Authentication Methods

### 1. API Key Authentication (Recommended)

Used for Elastic Cloud and modern deployments:

```javascript
const client = new Client({
  node: 'https://your-cluster.es.cloud:443',
  auth: {
    apiKey: 'your_api_key_here'
  },
  serverMode: 'serverless' // For Elastic Cloud serverless
});
```

### 2. Username/Password Authentication

Used for traditional Elasticsearch deployments:

```javascript
const client = new Client({
  node: 'http://localhost:9200',
  auth: {
    username: 'elastic',
    password: 'your_password'
  }
});
```

## Index Configuration

The system uses a single index (default: `befarma`) for all agricultural data with the following mapping:

```json
{
  "mappings": {
    "properties": {
      "text": { "type": "text" },
      "cropId": { "type": "keyword" },
      "farmId": { "type": "keyword" },
      "sellerId": { "type": "keyword" },
      "name": { "type": "text", "analyzer": "standard" },
      "type": { "type": "keyword" },
      "variety": { "type": "text" },
      "growthStage": { "type": "keyword" },
      "healthStatus": { "type": "keyword" },
      "plantingDate": { "type": "date" },
      "expectedHarvestDate": { "type": "date" },
      "createdAt": { "type": "date" },
      "updatedAt": { "type": "date" }
    }
  }
}
```

## Testing the Configuration

### Connection Test

```bash
npm run test:connection
```

This script will:
1. Test cluster connectivity
2. Verify authentication
3. Check/create the index
4. Test document operations
5. Clean up test data

### Manual Testing

```javascript
// Test with Node.js
const { Client } = require('@elastic/elasticsearch');
require('dotenv').config();

const client = new Client({
  node: process.env.ELASTICSEARCH_NODE,
  auth: {
    apiKey: process.env.ELASTICSEARCH_API_KEY
  }
});

client.ping().then(console.log).catch(console.error);
```

## Service Integration

### Crop Service

The crop service uses Elasticsearch for:
- Full-text search across crop data
- Real-time indexing of crop lifecycle events
- Advanced filtering and aggregations
- Analytics and reporting

### Farm Service

The farm service uses Elasticsearch for:
- Farm location-based searches
- Farm metadata indexing
- Cross-farm analytics

### Configuration in Services

Each service reads the configuration from environment variables:

```javascript
// apps/crop-service/src/config/db/index.ts
export const connectElasticsearch = (): Client => {
  const clientOptions = {
    node: process.env.ELASTICSEARCH_NODE,
  };

  if (process.env.ELASTICSEARCH_API_KEY) {
    clientOptions.auth = { apiKey: process.env.ELASTICSEARCH_API_KEY };
    if (process.env.ELASTICSEARCH_SERVERLESS === 'true') {
      clientOptions.serverMode = 'serverless';
    }
  }

  return new Client(clientOptions);
};
```

## Troubleshooting

### Common Issues

1. **Connection Refused**
   - Check if Elasticsearch is running
   - Verify the node URL
   - Check firewall settings

2. **Authentication Failed**
   - Verify API key or credentials
   - Check if credentials have expired
   - Ensure proper permissions

3. **Index Not Found**
   - Run the connection test to create the index
   - Check index name configuration
   - Verify cluster permissions

4. **SSL/TLS Issues**
   - Set `ELASTICSEARCH_SSL_VERIFY=false` for development
   - Check certificate validity
   - Verify TLS configuration

### Debug Mode

Enable debug logging:

```env
DEBUG=elasticsearch:*
```

### Health Check

Check service health:

```bash
curl http://localhost:3004/api/health
```

## Production Considerations

### Security

1. Use API keys instead of username/password
2. Enable SSL/TLS verification
3. Restrict network access
4. Rotate credentials regularly

### Performance

1. Configure appropriate shard count
2. Set up proper replicas
3. Monitor cluster health
4. Optimize queries and mappings

### Monitoring

1. Set up cluster monitoring
2. Configure alerts
3. Monitor index size and performance
4. Track query performance

## Migration Guide

### From Local to Cloud

1. Export data from local Elasticsearch
2. Configure cloud credentials
3. Update environment variables
4. Import data to cloud cluster
5. Test all services

### Index Migration

```bash
# Reindex all data
curl -X POST "localhost:3004/api/crops/reindex"
curl -X POST "localhost:3003/api/farms/reindex"
```

## Support

For issues with:
- **Befarma Cloud**: Contact Befarma support
- **Local Setup**: Check Elasticsearch documentation
- **AgriTech Integration**: Review service logs and documentation
