#!/usr/bin/env node

const jwt = require('jsonwebtoken');

const token = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************.genNzCYmQpiM_fLE2PRXh1FcU6naQCLi9vd_COPntRc';
const JWT_SECRET = 'befarma_';

console.log('🎉 JWT Token Successfully Generated!');
console.log('=====================================');
console.log('');

// Decode without verification to see structure
const decoded = jwt.decode(token);
console.log('📋 Token Payload:');
console.log(JSON.stringify(decoded, null, 2));
console.log('');

// Verify with correct secret
try {
  const verified = jwt.verify(token, JWT_SECRET);
  console.log('✅ Token Verification: SUCCESS');
  console.log('🔐 Secret used: befarma_');
  console.log('');
  
  // Show expiry info
  const now = Math.floor(Date.now() / 1000);
  const timeUntilExpiry = verified.exp - now;
  const hoursUntilExpiry = Math.floor(timeUntilExpiry / 3600);
  const minutesUntilExpiry = Math.floor((timeUntilExpiry % 3600) / 60);
  
  console.log('⏰ Token Timing:');
  console.log(`   Issued at: ${new Date(verified.iat * 1000).toLocaleString()}`);
  console.log(`   Expires at: ${new Date(verified.exp * 1000).toLocaleString()}`);
  console.log(`   Time until expiry: ${hoursUntilExpiry}h ${minutesUntilExpiry}m`);
  console.log('');
  
  console.log('👤 User Information:');
  console.log(`   ID: ${verified.id}`);
  console.log(`   Name: ${verified.name}`);
  console.log(`   Email: ${verified.email}`);
  console.log(`   Role: ${verified.role}`);
  console.log('');
  
} catch (error) {
  console.log('❌ Token Verification: FAILED');
  console.log(`🚨 Error: ${error.message}`);
}

console.log('🚀 Next Steps:');
console.log('   1. Use this token in Authorization header: Bearer <token>');
console.log('   2. Test protected endpoints with this token');
console.log('   3. Token is valid for 24 hours from generation');
console.log('');
console.log('📝 Example Usage:');
console.log('   Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...');
