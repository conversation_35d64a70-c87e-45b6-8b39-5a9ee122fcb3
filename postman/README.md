# Postman Collections

This directory contains Postman collections for testing all AgriTech microservices APIs.

## Collections Overview

### Available Collections
- `seller-service.json` - Seller registration, authentication, and profile management (✅ Ready)
- `farm-service.json` - **UPDATED** - Comprehensive farm management across all services (✅ Ready)
- `crop-service.json` - Crop management, marketplace, and enhanced search (✅ Ready)
- `admin-service.json` - Administrative functions and system management (✅ Ready)
- `analytics-service.json` - Analytics, reporting, and business intelligence (✅ Ready)
- `notification-service.json` - Notification management and communication (✅ Ready)
- `order-service.json` - Order processing and lifecycle management (✅ Ready)

### Environment File
- `AgriTech-Environment.json` - Complete environment configuration with all variables (✅ Ready)

### Environment Variables
Each collection uses the following environment variables:

```json
{
  "seller_service_url": "http://localhost:3001",
  "farm_service_url": "http://localhost:3005",
  "crop_service_url": "http://localhost:3004",
  "admin_service_url": "http://localhost:3002",
  "analytics_service_url": "http://localhost:3003",
  "notification_service_url": "http://localhost:3008",
  "order_service_url": "http://localhost:3009",
  "jwt_token": "{{auth_token}}",
  "admin_token": "{{admin_auth_token}}"
}
```

## Quick Setup Instructions

### 🚀 **One-Click Import**
1. **Download all files** from this directory
2. **Open Postman** → Click **"Import"** button
3. **Drag and drop ALL JSON files** into the import dialog
4. **Import everything** - collections and environment will be set up automatically!

### 📋 **Step-by-Step Setup**

#### 1. Import Collections (7 files)
```bash
# Import these collection files in any order:
✅ seller-service.json
✅ admin-service.json
✅ analytics-service.json
✅ crop-service.json
✅ farm-service.json
✅ notification-service.json
✅ order-service.json
```

#### 2. Import Environment
```bash
# Import the environment file:
✅ AgriTech-Environment.json
```

#### 3. Activate Environment
1. In Postman, select **"AgriTech Development Environment"** from the environment dropdown
2. All variables will be automatically configured!

### 🔐 **Authentication Setup (Important!)**
**You MUST authenticate before using protected endpoints:**

#### Option A: Quick Login (Recommended)
1. **Seller Login**: Use `Seller Service → Authentication → Seller Login`
   - Email: `<EMAIL>`
   - Password: `farmer123`
   - ✅ Token automatically saved to environment

2. **Admin Login**: Use `Admin Service → Authentication → Admin Login`
   - Email: `<EMAIL>`
   - Password: `superadmin123`
   - ✅ Admin token automatically saved to environment

#### Option B: Manual Token Setup
1. Get JWT tokens from authentication endpoints
2. Set tokens in environment variables:
   - `jwt_token` - for regular user access
   - `admin_token` - for admin access

## 🧪 **Complete Testing Workflow**

### 🔄 **Recommended Testing Sequence**

#### **Phase 1: Authentication & Setup**
1. **🔐 Authenticate as Seller**
   ```
   Seller Service → Authentication → Seller Login
   ✅ Saves jwt_token automatically
   ```

2. **🔐 Authenticate as Admin**
   ```
   Admin Service → Authentication → Admin Login
   ✅ Saves admin_token automatically
   ```

#### **Phase 2: Core Data Creation**
3. **🏭 Create Farm (Multiple Options)**
   ```
   Option A: Farm Service → Farm Service - Direct Farm Management → Create Farm
   Option B: Seller Service → Seller Service - Farm Management → Create Farm via Seller Service
   ✅ Both save farm_id automatically
   ```

4. **🌾 Create Crop**
   ```
   Crop Service → Crop CRUD Operations → Create Crop
   ✅ Saves crop_id automatically
   ```

5. **📦 Create Order**
   ```
   Order Service → Order Management → Create Order
   ✅ Saves order_id automatically
   ```

#### **Phase 3: Advanced Features**
6. **🔍 Test Search Features**
   ```
   - Farm Service → Farm Search & Discovery → Search Farms
   - Crop Service → Crop Search & Discovery → Search Crops
   - Seller Service → Search & Analytics → Search Sellers
   ```

7. **📊 Test Analytics**
   ```
   - Analytics Service → Dashboard Analytics → Get Dashboard Overview
   - Analytics Service → Sales Analytics → Get Sales Overview
   - Analytics Service → Crop Analytics → Get Crop Performance
   ```

8. **🔔 Test Notifications**
   ```
   - Notification Service → Notification Management → Send Notification
   - Notification Service → Bulk Notifications → Send Bulk Notifications
   ```

#### **Phase 4: Admin Functions**
9. **👨‍💼 Test Admin Features**
   ```
   - Admin Service → Seller Management → Get All Sellers
   - Admin Service → Farm Management → Verify Farm
   - Admin Service → Dashboard & Analytics → Admin Dashboard
   ```

### 🎯 **Quick Test Scenarios**

#### **Scenario A: New Farmer Onboarding**
```
1. Seller Service → Authentication → Seller Register
2. Seller Service → KYC & Documents → Upload KYC Document
3. Admin Service → Seller Management → Verify Seller
4. Farm Service → Farm CRUD Operations → Create Farm
5. Crop Service → Crop CRUD Operations → Create Crop
```

#### **Scenario B: Order Processing**
```
1. Order Service → Order Management → Create Order
2. Order Service → Order Lifecycle → Accept Order (Seller)
3. Order Service → Payment Processing → Process Payment
4. Order Service → Order Lifecycle → Mark as Shipped
5. Order Service → Order Lifecycle → Mark as Delivered
```

#### **Scenario C: Market Analytics**
```
1. Analytics Service → Market Intelligence → Get Market Insights
2. Analytics Service → Market Intelligence → Get Price Predictions
3. Analytics Service → Report Generation → Generate Custom Report
4. Analytics Service → Data Export → Export Analytics Data
```

## 🚨 **UPDATED: Farm Service Collection v2.0**

### **What's New in Farm Collection**
The farm collection has been completely updated to provide comprehensive farm management across all services:

#### **🏗️ Multi-Service Architecture**
- **Farm Service (Port 3005)**: Direct farm management and Elasticsearch operations
- **Seller Service (Port 3001)**: Seller-specific farm operations with authentication
- **Admin Service (Port 3002)**: Administrative farm verification and management

#### **📋 Complete API Coverage**
1. **Health Checks**: All three services health endpoints
2. **Farm Service - Direct Management**:
   - Query/Create/Update/Delete farms
   - Elasticsearch search and analytics
   - Farm reindexing utilities
3. **Seller Service - Farm Management**:
   - Authenticated farm operations
   - Crop rotation plan management
   - Seller-specific farm analytics
4. **Admin Service - Farm Administration**:
   - Admin farm verification
   - System-wide farm management
5. **Data Management**: Seeding and statistics utilities

#### **🔧 Enhanced Features**
- **Proper Authentication**: JWT tokens for all protected endpoints
- **Auto-Variable Setting**: Farm IDs automatically saved for chaining requests
- **Comprehensive Testing**: Response validation and error handling
- **Updated Schemas**: Latest farm data structure with crop rotation, farming practices
- **Environment Variables**: Separate URLs for each service

#### **🎯 Testing Workflow for Farms**
```
1. Health Checks → Test all services are running
2. Authentication → Get JWT token
3. Farm Service → Create farm directly
4. Seller Service → Create farm via seller
5. Admin Service → Verify farm
6. Search & Analytics → Test discovery features
7. Data Management → Seed additional test data
```

### Collection-Specific Notes

#### Seller Service Collection
- Complete seller onboarding flow
- KYC document upload and verification
- Profile management and bank details
- Farm management endpoints

#### Farm Service Collection (UPDATED v2.0)
- **Multi-Service Integration**: Farm Service (3005), Seller Service (3001), Admin Service (3002)
- **Complete CRUD Operations**: Create, read, update, delete farms across all services
- **Enhanced Search**: Elasticsearch-powered search with advanced filtering
- **Crop Rotation Management**: Update and manage crop rotation plans
- **Farm Analytics**: Comprehensive analytics and performance metrics
- **Admin Verification**: Farm verification and compliance workflows
- **Authentication**: Proper JWT token handling for all endpoints
- **Data Seeding**: Utilities for seeding test farm data
- **Comprehensive Testing**: Automated test scripts with response validation

#### Crop Service Collection
- Comprehensive crop lifecycle management
- Enhanced search with multiple filters
- Marketplace functionality
- Real-time analytics and insights

#### Admin Service Collection
- User and seller management
- System administration
- Data verification workflows
- Analytics and reporting

#### Analytics Service Collection
- Dashboard data retrieval
- Custom report generation
- Market intelligence queries
- Data export functionality

#### Notification Service Collection
- Notification sending and management
- Template management
- User preferences
- Real-time notification testing

#### Order Service Collection
- Complete order lifecycle
- Payment processing
- Order tracking
- Analytics and reporting

## Pre-request Scripts

Each collection includes pre-request scripts for:
- Automatic token refresh
- Environment variable validation
- Request data generation
- Timestamp formatting

## Test Scripts

Collections include test scripts for:
- Response status validation
- Response schema validation
- Data integrity checks
- Performance benchmarks

## Running Collections

### Manual Testing
1. Select a collection
2. Choose requests to run
3. Verify responses and data

### Automated Testing
1. Use Postman Runner for collection execution
2. Set up data files for parameterized testing
3. Configure test environments for different stages

### CI/CD Integration
1. Export collections and environments
2. Use Newman CLI for automated testing
3. Integrate with build pipelines

```bash
# Install Newman
npm install -g newman

# Run a collection
newman run seller-service.json -e environment.json

# Run with data file
newman run crop-service.json -e environment.json -d test-data.csv

# Generate HTML report
newman run admin-service.json -e environment.json -r html
```

## Troubleshooting

### Common Issues
1. **Authentication Errors**: Ensure JWT tokens are valid and not expired
2. **Network Errors**: Verify service URLs and network connectivity
3. **Rate Limiting**: Wait for rate limit reset or use different endpoints
4. **Data Dependencies**: Ensure prerequisite data exists before testing

### Debug Tips
1. Enable Postman Console for detailed logs
2. Use environment variables for dynamic data
3. Check service health endpoints first
4. Verify request headers and body format

## Contributing

When adding new endpoints:
1. Update the relevant collection file
2. Add appropriate tests and pre-request scripts
3. Update environment variables if needed
4. Document any special requirements

## Support

For issues with collections:
1. Check service documentation first
2. Verify environment setup
3. Test individual requests before running collections
4. Report issues with detailed error information
