# Crop Service Postman Collection Update

## Overview
The Crop Service Postman collection has been updated to match the actual API implementation and DTO requirements. The previous collection had outdated field structures that didn't align with the current crop service schema.

## Key Changes Made

### 1. Fixed Create Crop Request Body ✅
**Previous Issues:**
- Used outdated field names (`attributes`, `cultivation.plantingDate`, etc.)
- Missing required fields like `plotId`, `soilConditions`, `metadata`
- Incorrect structure for nested objects

**Updated Structure:**
```json
{
  "plotId": "{{plot_id}}",
  "farmId": "{{farm_id}}",
  "sellerId": "{{seller_id}}",
  "name": "Organic Cherry Tomatoes",
  "type": "VEGETABLE",
  "variety": "Cherry",
  "plantingDate": "2024-01-15T00:00:00.000Z",
  "expectedHarvestDate": "2024-04-15T00:00:00.000Z",
  "soilConditions": {
    "type": "LOAMY",
    "ph": 6.5,
    "nutrients": ["NITROGEN", "PHOSPHORUS", "POTASSIUM"]
  },
  "waterAvailability": "ADEQUATE",
  "metadata": {
    "cropCategory": "VEGETABLES",
    "farmingMethod": "ORGANIC",
    "irrigationMethod": "DRIP",
    "harvestSeason": "SUMMER",
    "waterSource": "BOREWELL",
    "seedType": "HYBRID"
  },
  "cultivation": {
    "irrigationNeeds": "Regular watering every 2-3 days",
    "fertilizerRequirements": "Organic compost and vermicompost",
    "pestControl": "Neem oil and organic pesticides",
    "climateConditions": "Warm and humid climate preferred"
  },
  "yield": {
    "expected": 5000,
    "actual": 0,
    "unit": "KG"
  },
  "resources": {
    "water": 1000,
    "fertilizer": 50,
    "pesticides": 10
  },
  "health": {
    "status": "HEALTHY",
    "issues": [],
    "lastCheck": "2024-01-15T00:00:00.000Z"
  },
  "images": ["https://example.com/tomato1.jpg"],
  "tags": ["organic", "cherry", "tomato", "fresh"],
  "certifications": ["ORGANIC", "PESTICIDE_FREE"]
}
```

### 2. Updated Search Request Parameters ✅
**Previous Issues:**
- Used `q` instead of `query` parameter
- Had marketplace-specific filters not supported by the API

**Updated Parameters:**
- `query` - Search text
- `sellerId` - Filter by seller
- `farmId` - Filter by farm
- `plotId` - Filter by plot
- `growthStage` - Filter by growth stage
- `healthStatus` - Filter by health status
- `page` - Page number for pagination
- `limit` - Results per page
- `harvestDateFrom` - Date range filter (from)
- `harvestDateTo` - Date range filter (to)

### 3. Added Missing Variables ✅
```json
{
  "key": "seller_id",
  "value": "seller_123",
  "type": "string"
},
{
  "key": "farm_id",
  "value": "farm_456",
  "type": "string"
},
{
  "key": "plot_id",
  "value": "plot_789",
  "type": "string"
}
```

### 4. Added New Endpoints ✅

#### Health Check Endpoints:
- **Service Health Check**: `GET /api/health`
- **Crop Service Health Check**: `GET /api/crops/health`
- **Test Search Functionality**: `GET /api/crops/test-search?q=tomato`

#### Crop Lifecycle Management:
- **Update Growth Stage**: `PUT /api/crops/{cropId}/growth-stage`
- **Update Health Status**: `PUT /api/crops/{cropId}/health`
- **Record Harvest**: `POST /api/crops/{cropId}/harvest`
- **Add Maintenance Activity**: `POST /api/crops/{cropId}/maintenance`
- **Get Crop Analytics**: `GET /api/crops/analytics/{sellerId}`
- **Reindex All Crops**: `POST /api/crops/reindex`

### 5. Updated Update Crop Request ✅
Now includes proper fields that match the UpdateCropDto:
```json
{
  "name": "Updated Organic Cherry Tomatoes",
  "actualHarvestDate": "2024-04-12T00:00:00.000Z",
  "growthStage": "HARVESTED",
  "yield": {
    "actual": 4500,
    "unit": "KG"
  },
  "health": {
    "status": "HEALTHY",
    "issues": [],
    "lastCheck": "2024-04-12T00:00:00.000Z"
  },
  "resources": {
    "water": 1200,
    "fertilizer": 60,
    "pesticides": 15
  },
  "tags": ["organic", "cherry", "tomato", "fresh", "harvested"]
}
```

## Required Fields for Create Crop

### Mandatory Fields:
- `plotId` - Plot identifier
- `farmId` - Farm identifier  
- `sellerId` - Seller identifier
- `name` - Crop name
- `type` - Crop type
- `variety` - Crop variety
- `plantingDate` - Planting date (ISO format)
- `expectedHarvestDate` - Expected harvest date (ISO format)
- `soilConditions` - Soil conditions object
- `waterAvailability` - Water availability status
- `metadata` - Metadata object with crop details
- `cultivation` - Cultivation requirements

### Optional Fields:
- `actualHarvestDate` - Actual harvest date
- `growthStage` - Current growth stage (defaults to PLANTING)
- `health` - Health status object
- `yield` - Yield information
- `resources` - Resource usage
- `nutritionalInfo` - Nutritional information
- `postHarvest` - Post-harvest details
- `sustainability` - Sustainability metrics
- `maintenance` - Maintenance schedule and history
- `images` - Image URLs
- `tags` - Tags array
- `certifications` - Certifications array

## How to Use the Updated Collection

### 1. Set Variables
Before making requests, set these collection variables:
- `seller_id` - Your seller ID
- `farm_id` - Your farm ID  
- `plot_id` - Your plot ID
- `jwt_token` - Authentication token (if required)

### 2. Test the Service
1. **Health Check**: Run "Crop Service Health Check" to verify service status
2. **Test Search**: Run "Test Search Functionality" to verify search is working
3. **Create Crop**: Use the updated "Create Crop" request with all required fields
4. **Search Crops**: Use the updated search with proper query parameters

### 3. Lifecycle Management
After creating a crop, you can:
1. Update growth stage as the crop develops
2. Record health status changes
3. Add maintenance activities
4. Record harvest when ready
5. View analytics for your crops

## Troubleshooting

### Common Issues:
1. **Missing Required Fields**: Ensure all mandatory fields are provided in create requests
2. **Date Format**: Use ISO 8601 format for dates (`2024-01-15T00:00:00.000Z`)
3. **Variable Values**: Set proper values for `seller_id`, `farm_id`, and `plot_id`
4. **Authentication**: Set `jwt_token` if authentication is required

### Validation Errors:
If you get validation errors, check:
- All required fields are present
- Date formats are correct
- Enum values match expected values (e.g., growth stages, health status)
- Nested objects have required properties

The updated collection now properly aligns with the actual crop service implementation and should work without field validation errors.
