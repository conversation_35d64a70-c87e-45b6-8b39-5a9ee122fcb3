# AgriTech Backend Service Ports

This document provides a reference for all active microservice ports in the AgriTech seller backend.

## Active Services

| Service | Port | Status |
|---------|------|--------|
| Seller Service | 3001 | ✅ Active |
| Admin Service | 3002 | ✅ Active |
| Analytics Service | 3003 | ✅ Active |
| Crop Service | 3004 | ✅ Active |
| Farm Service | 3005 | ✅ Active |
| Notification Service | 3008 | ✅ Active |
| Order Service | 3009 | ✅ Active |

## Deprecated Services (Removed)

| Service | Port | Status |
|---------|------|--------|
| API Gateway | 3000 | ❌ Deprecated |
| Financial Service | 3006 | ❌ Deprecated |
| Inventory Service | 3007 | ❌ Deprecated |
| Plot Service | 3010 | ❌ Deprecated |

**Note**: Plot functionality has been integrated into the Farm Service. Financial and Inventory features are handled by existing services.

## Environment Variables

Each service has the following environment variables:

- `PORT`: The port on which the service runs
- `NODE_ENV`: The environment (development, production, etc.)
- `SERVICE_NAME`: The name of the service

These are defined in the `.env` file within each service directory. 