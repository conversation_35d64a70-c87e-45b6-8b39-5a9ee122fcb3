# Farm Service API Documentation

## Overview
The Farm Service manages all farm-related operations in the AgriTech platform, including farm registration, management, and analytics.

## Base URL
```
http://localhost:3005/api/farms
```

## Authentication
All endpoints require JWT authentication. Include the JWT token in the Authorization header:
```
Authorization: Bearer <jwt_token>
```

## Endpoints

### Farm Management

#### Create Farm
- **POST** `/`
- Creates a new farm
- Request body: `CreateFarmDto`
- Response: Created farm details

#### Get Farm by ID
- **GET** `/:farmId`
- Retrieves farm details by ID
- Response: Farm details

#### Update Farm
- **PUT** `/:farmId`
- Updates farm details
- Request body: `UpdateFarmDto`
- Response: Updated farm details

#### Delete Farm
- **DELETE** `/:farmId`
- Deletes a farm
- Response: Success message

### Search & Discovery

#### Get Minimal Farm Details
- **GET** `/minimal/:sellerId`
- Retrieves minimal farm details for dropdowns
- Response: Array of minimal farm details
```json
{
  "success": true,
  "data": [
    {
      "farmId": "farm-123",
      "name": "Green Acres",
      "location": {
        "state": "Karnataka",
        "city": "Bangalore"
      },
      "status": "ACTIVE"
    }
  ]
}
```

#### Search Farms
- **GET** `/search`
- Search farms using Elasticsearch
- Query parameters:
  - `q`: Search query
  - `location`: Coordinates (lat,lng)
  - `radius`: Search radius in km
  - Additional filters available
- Response: Matching farms with pagination

#### Get Farm Analytics
- **GET** `/analytics/:sellerId`
- Get analytics for farms by seller
- Response: Analytics data

### Data Management

#### Reindex Farms
- **POST** `/reindex`
- Reindex all farms in Elasticsearch
- Admin only
- Response: Success message

## Data Transfer Objects (DTOs)

### CreateFarmDto
```typescript
{
  sellerId: string;
  name: string;
  location: LocationDto;
  totalArea: number;
  soilType: string;
  waterSource: string;
  infrastructure?: string[];
  certifications?: string[];
}
```

### UpdateFarmDto
```typescript
{
  name?: string;
  location?: LocationDto;
  totalArea?: number;
  soilType?: string;
  waterSource?: string;
  infrastructure?: string[];
  certifications?: string[];
  status?: 'ACTIVE' | 'INACTIVE';
}
```

### MinimalFarmResponseDto
```typescript
{
  farmId: string;
  name: string;
  location: {
    state: string;
    city: string;
  };
  status: 'ACTIVE' | 'INACTIVE';
}
```

## Error Handling
All endpoints follow standard error response format:
```json
{
  "success": false,
  "message": "Error message",
  "error": "Detailed error information"
}
```

## Status Codes
- 200: Success
- 201: Created
- 400: Bad Request
- 401: Unauthorized
- 403: Forbidden
- 404: Not Found
- 500: Internal Server Error 