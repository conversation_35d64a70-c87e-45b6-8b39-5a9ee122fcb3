import mongoose from 'mongoose';
import { runAllSeeds } from '../seeds';

/**
 * Main seed function that runs all seeding processes
 */
async function seedDb() {
  try {
    // Connect to MongoDB
    const mongoUri = process.env['MONGODB_URI'] || 'mongodb://localhost:27017/agritech';
    console.log(`Connecting to MongoDB at ${mongoUri}...`);
    
    await mongoose.connect(mongoUri);
    console.log('Connected to MongoDB');
    
    // Run all seeders (enums, admins, sellers, farms, plots, crops)
    await runAllSeeds();
    console.log('Database seeding completed');
    
    // Close connection
    await mongoose.connection.close();
    console.log('Database seeding completed, MongoDB connection closed');
    
    // Only exit if run directly
    if (require.main === module) {
      process.exit(0);
    }
  } catch (error) {
    console.error('Error seeding database:', error);
    
    // Close connection on error
    if (mongoose.connection.readyState !== 0) {
      await mongoose.connection.close();
    }
    // Only exit if run directly
    if (require.main === module) {
      process.exit(1);
    } else {
      throw error;
    }
  }
}

// Run the seed function if this script is executed directly
if (require.main === module) {
  seedDb();
}

export default seedDb; 