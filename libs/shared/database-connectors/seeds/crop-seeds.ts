import { CropModel, CropGrowthStage, CropHealthStatus } from '../schemas/mongo/crop.schema';
import { v4 as uuidv4 } from 'uuid';
import { Client } from '@elastic/elasticsearch';
import { initElasticSearch } from '../elastic.connector';

/**
 * Comprehensive crop seeds with proper farm-crop rotation system
 * This represents realistic crop rotations across different seasons for farms
 */
export const cropSeeds = [
  // FARM-001 (Haneef's Paddy Farm) - Crop Rotation: Rice -> Wheat -> Vegetables
  {
    cropId: 'CROP-001',
    numberOfPlots: 2,
    farmId: 'FARM-001',
    sellerId: 'FARMER-001',
    name: 'Basmati Rice',
    type: 'Cereal',
    variety: 'Basmati 370',
    plantingDate: new Date('2024-06-15T00:00:00Z'), // Kharif season
    expectedHarvestDate: new Date('2024-10-30T00:00:00Z'),
    growthStage: CropGrowthStage.GROWING,
    health: {
      status: CropHealthStatus.HEALTHY,
      issues: [],
      lastCheck: new Date()
    },
    yield: {
      expected: 6000,
      actual: 0,
      unit: 'kg'
    },
    resources: {
      water: 2500, // Rice requires more water
      fertilizer: 80,
      pesticides: 10
    },
    soilConditions: {
      type: 'Alluvial',
      ph: 6.8,
      nutrients: ['N', 'P', 'K']
    },
    waterAvailability: 'Canal',
    metadata: {
      cropCategory: 'Cereals',
      farmingMethod: 'Conventional',
      irrigationMethod: 'Flood Irrigation',
      harvestSeason: 'Kharif',
      pestDiseaseStatus: 'None',
      storageMethod: 'Warehouse Storage',
      nutrientManagement: 'Balanced Fertilizers',
      waterSource: 'Canal Water',
      pesticideUsage: 'Integrated Pest Management',
      seedType: 'Certified',
      harvestingMethod: 'Mechanical'
    },
    nutritionalInfo: {
      calories: 130,
      protein: 2.7,
      carbs: 28,
      fiber: 0.4,
      vitamins: ['B1', 'B3', 'B6']
    },
    cultivation: {
      irrigationNeeds: 'High',
      fertilizerRequirements: 'High',
      pestControl: 'Integrated',
      climateConditions: 'Hot and Humid'
    },
    postHarvest: {
      storageRequirements: 'Dry, ventilated storage',
      shelfLife: '12 months',
      processingMethods: ['Drying', 'Milling', 'Polishing']
    },
    sustainability: {
      waterUsage: 'High',
      carbonFootprint: 'Medium',
      pesticide: 'Moderate'
    },
    maintenance: {
      schedule: {
        irrigation: [new Date('2024-06-20T00:00:00Z'), new Date('2024-07-01T00:00:00Z')],
        fertilization: [new Date('2024-06-25T00:00:00Z'), new Date('2024-08-15T00:00:00Z')],
        pestControl: [new Date('2024-07-10T00:00:00Z'), new Date('2024-08-20T00:00:00Z')],
        inspection: [new Date('2024-07-05T00:00:00Z'), new Date('2024-08-05T00:00:00Z')]
      },
      history: {
        activities: []
      }
    },
    weather: {
      forecasts: [],
      alerts: []
    },
    images: [],
    tags: ['basmati', 'rice', 'kharif'],
    certifications: ['Quality Certification'],
    createdAt: new Date(),
    updatedAt: new Date()
  },

  // FARM-001 - Rabi Season Crop (Wheat after Rice)
  {
    cropId: 'CROP-002',
    numberOfPlots: 3,
    farmId: 'FARM-001',
    sellerId: 'FARMER-001',
    name: 'Wheat',
    type: 'Cereal',
    variety: 'HD-2967',
    plantingDate: new Date('2024-11-15T00:00:00Z'), // Rabi season after rice harvest
    expectedHarvestDate: new Date('2025-04-15T00:00:00Z'),
    growthStage: CropGrowthStage.PLANTING,
    health: {
      status: CropHealthStatus.HEALTHY,
      issues: [],
      lastCheck: new Date()
    },
    yield: {
      expected: 4500,
      actual: 0,
      unit: 'kg'
    },
    resources: {
      water: 1200,
      fertilizer: 60,
      pesticides: 8
    },
    soilConditions: {
      type: 'Alluvial',
      ph: 7.2,
      nutrients: ['N', 'P', 'K']
    },
    waterAvailability: 'Canal',
    metadata: {
      cropCategory: 'Cereals',
      farmingMethod: 'Conventional',
      irrigationMethod: 'Sprinkler Irrigation',
      harvestSeason: 'Rabi',
      pestDiseaseStatus: 'None',
      storageMethod: 'Warehouse Storage',
      nutrientManagement: 'NPK Fertilizers',
      waterSource: 'Canal Water',
      pesticideUsage: 'Selective Application',
      seedType: 'High Yielding Variety',
      harvestingMethod: 'Mechanical'
    },
    nutritionalInfo: {
      calories: 340,
      protein: 13.2,
      carbs: 71.2,
      fiber: 12.2,
      vitamins: ['B1', 'B3', 'E']
    },
    cultivation: {
      irrigationNeeds: 'Moderate',
      fertilizerRequirements: 'High',
      pestControl: 'Integrated',
      climateConditions: 'Cool and Dry'
    },
    postHarvest: {
      storageRequirements: 'Dry, pest-free storage',
      shelfLife: '18 months',
      processingMethods: ['Threshing', 'Cleaning', 'Milling']
    },
    sustainability: {
      waterUsage: 'Moderate',
      carbonFootprint: 'Low',
      pesticide: 'Low'
    },
    maintenance: {
      schedule: {
        irrigation: [new Date('2024-12-01T00:00:00Z'), new Date('2025-01-15T00:00:00Z')],
        fertilization: [new Date('2024-11-25T00:00:00Z'), new Date('2025-02-01T00:00:00Z')],
        pestControl: [new Date('2025-01-10T00:00:00Z'), new Date('2025-03-01T00:00:00Z')],
        inspection: [new Date('2024-12-15T00:00:00Z'), new Date('2025-02-15T00:00:00Z')]
      },
      history: {
        activities: []
      }
    },
    weather: {
      forecasts: [],
      alerts: []
    },
    images: [],
    tags: ['wheat', 'rabi', 'cereal'],
    certifications: ['Quality Certification'],
    createdAt: new Date(),
    updatedAt: new Date()
  },

  // FARM-001 - Summer/Zaid Season Crop (Vegetables after Wheat)
  {
    cropId: 'CROP-003',
    numberOfPlots: 1,
    farmId: 'FARM-001',
    sellerId: 'FARMER-001',
    name: 'Tomatoes',
    type: 'Vegetable',
    variety: 'Roma',
    plantingDate: new Date('2025-05-01T00:00:00Z'), // Zaid season after wheat harvest
    expectedHarvestDate: new Date('2025-07-15T00:00:00Z'),
    growthStage: CropGrowthStage.PLANTING,
    health: {
      status: CropHealthStatus.HEALTHY,
      issues: [],
      lastCheck: new Date()
    },
    yield: {
      expected: 3500,
      actual: 0,
      unit: 'kg'
    },
    resources: {
      water: 800,
      fertilizer: 40,
      pesticides: 6
    },
    soilConditions: {
      type: 'Alluvial',
      ph: 6.5,
      nutrients: ['N', 'P', 'K']
    },
    waterAvailability: 'Canal',
    metadata: {
      cropCategory: 'Vegetables',
      farmingMethod: 'Organic',
      irrigationMethod: 'Drip Irrigation',
      harvestSeason: 'Zaid',
      pestDiseaseStatus: 'None',
      storageMethod: 'Cold Storage',
      nutrientManagement: 'Organic Fertilizers',
      waterSource: 'Canal Water',
      pesticideUsage: 'Organic Pesticides',
      seedType: 'Heirloom',
      harvestingMethod: 'Manual'
    },
    nutritionalInfo: {
      calories: 18,
      protein: 0.9,
      carbs: 3.9,
      fiber: 1.2,
      vitamins: ['A', 'C', 'K']
    },
    cultivation: {
      irrigationNeeds: 'Moderate',
      fertilizerRequirements: 'Low',
      pestControl: 'Organic',
      climateConditions: 'Warm'
    },
    postHarvest: {
      storageRequirements: 'Cool, dry place',
      shelfLife: '2 weeks',
      processingMethods: ['Washing', 'Sorting']
    },
    sustainability: {
      waterUsage: 'Low',
      carbonFootprint: 'Low',
      pesticide: 'Organic'
    },
    maintenance: {
      schedule: {
        irrigation: [new Date('2025-05-05T00:00:00Z'), new Date('2025-06-01T00:00:00Z')],
        fertilization: [new Date('2025-05-10T00:00:00Z'), new Date('2025-06-15T00:00:00Z')],
        pestControl: [new Date('2025-05-20T00:00:00Z'), new Date('2025-06-25T00:00:00Z')],
        inspection: [new Date('2025-05-15T00:00:00Z'), new Date('2025-06-10T00:00:00Z')]
      },
      history: {
        activities: []
      }
    },
    weather: {
      forecasts: [],
      alerts: []
    },
    images: [],
    tags: ['organic', 'tomato', 'zaid'],
    certifications: ['Organic Certification'],
    createdAt: new Date(),
    updatedAt: new Date()
  },

  // FARM-002 (Different Farm) - Cotton Crop
  {
    cropId: 'CROP-004',
    numberOfPlots: 4,
    farmId: 'FARM-002',
    sellerId: 'FARMER-002',
    name: 'Cotton',
    type: 'Fiber',
    variety: 'BT Cotton',
    plantingDate: new Date('2024-05-15T00:00:00Z'), // Kharif season
    expectedHarvestDate: new Date('2024-11-30T00:00:00Z'),
    growthStage: CropGrowthStage.GROWING,
    health: {
      status: CropHealthStatus.HEALTHY,
      issues: [],
      lastCheck: new Date()
    },
    yield: {
      expected: 2800,
      actual: 0,
      unit: 'kg'
    },
    resources: {
      water: 1800,
      fertilizer: 90,
      pesticides: 12
    },
    soilConditions: {
      type: 'Black Cotton',
      ph: 7.2,
      nutrients: ['N', 'P', 'K']
    },
    waterAvailability: 'Deep Bore Well',
    metadata: {
      cropCategory: 'Fiber',
      farmingMethod: 'Conventional',
      irrigationMethod: 'Sprinkler Irrigation',
      harvestSeason: 'Kharif',
      pestDiseaseStatus: 'None',
      storageMethod: 'Warehouse Storage',
      nutrientManagement: 'Chemical Fertilizers',
      waterSource: 'Groundwater',
      pesticideUsage: 'Chemical Pesticides',
      seedType: 'BT Hybrid',
      harvestingMethod: 'Mechanical'
    },
    nutritionalInfo: {
      // Cotton is not consumed, but seeds can be processed for oil
      calories: 0,
      protein: 0,
      carbs: 0,
      fiber: 0,
      vitamins: []
    },
    cultivation: {
      irrigationNeeds: 'High',
      fertilizerRequirements: 'High',
      pestControl: 'Chemical',
      climateConditions: 'Hot and Semi-Arid'
    },
    postHarvest: {
      storageRequirements: 'Dry, ventilated storage',
      shelfLife: '6 months',
      processingMethods: ['Ginning', 'Baling', 'Seed Processing']
    },
    sustainability: {
      waterUsage: 'High',
      carbonFootprint: 'Medium',
      pesticide: 'High'
    },
    maintenance: {
      schedule: {
        irrigation: [new Date('2024-06-01T00:00:00Z'), new Date('2024-07-15T00:00:00Z')],
        fertilization: [new Date('2024-05-25T00:00:00Z'), new Date('2024-08-01T00:00:00Z')],
        pestControl: [new Date('2024-06-10T00:00:00Z'), new Date('2024-08-15T00:00:00Z')],
        inspection: [new Date('2024-06-05T00:00:00Z'), new Date('2024-07-20T00:00:00Z')]
      },
      history: {
        activities: []
      }
    },
    weather: {
      forecasts: [],
      alerts: []
    },
    images: [],
    tags: ['cotton', 'bt-cotton', 'kharif', 'fiber'],
    certifications: ['BT Cotton Certification'],
    createdAt: new Date(),
    updatedAt: new Date()
  },

  // FARM-002 - Rabi Season Crop (Mustard after Cotton)
  {
    cropId: 'CROP-005',
    numberOfPlots: 2,
    farmId: 'FARM-002',
    sellerId: 'FARMER-002',
    name: 'Mustard',
    type: 'Oilseed',
    variety: 'Pusa Bold',
    plantingDate: new Date('2024-12-01T00:00:00Z'), // Rabi season after cotton
    expectedHarvestDate: new Date('2025-03-30T00:00:00Z'),
    growthStage: CropGrowthStage.PLANTING,
    health: {
      status: CropHealthStatus.HEALTHY,
      issues: [],
      lastCheck: new Date()
    },
    yield: {
      expected: 1200,
      actual: 0,
      unit: 'kg'
    },
    resources: {
      water: 600,
      fertilizer: 35,
      pesticides: 4
    },
    soilConditions: {
      type: 'Black Cotton',
      ph: 7.0,
      nutrients: ['N', 'P', 'K']
    },
    waterAvailability: 'Deep Bore Well',
    metadata: {
      cropCategory: 'Oilseeds',
      farmingMethod: 'Conventional',
      irrigationMethod: 'Sprinkler Irrigation',
      harvestSeason: 'Rabi',
      pestDiseaseStatus: 'None',
      storageMethod: 'Warehouse Storage',
      nutrientManagement: 'NPK Fertilizers',
      waterSource: 'Groundwater',
      pesticideUsage: 'Selective Application',
      seedType: 'High Yielding Variety',
      harvestingMethod: 'Manual'
    },
    nutritionalInfo: {
      calories: 508,
      protein: 26.1,
      carbs: 28.1,
      fiber: 12.2,
      vitamins: ['A', 'C', 'K']
    },
    cultivation: {
      irrigationNeeds: 'Low',
      fertilizerRequirements: 'Moderate',
      pestControl: 'Minimal',
      climateConditions: 'Cool and Dry'
    },
    postHarvest: {
      storageRequirements: 'Dry, pest-free storage',
      shelfLife: '12 months',
      processingMethods: ['Threshing', 'Oil Extraction']
    },
    sustainability: {
      waterUsage: 'Low',
      carbonFootprint: 'Low',
      pesticide: 'Low'
    },
    maintenance: {
      schedule: {
        irrigation: [new Date('2024-12-15T00:00:00Z'), new Date('2025-02-01T00:00:00Z')],
        fertilization: [new Date('2024-12-10T00:00:00Z'), new Date('2025-01-15T00:00:00Z')],
        pestControl: [new Date('2025-01-10T00:00:00Z')],
        inspection: [new Date('2024-12-20T00:00:00Z'), new Date('2025-02-15T00:00:00Z')]
      },
      history: {
        activities: []
      }
    },
    weather: {
      forecasts: [],
      alerts: []
    },
    images: [],
    tags: ['mustard', 'oilseed', 'rabi'],
    certifications: ['Quality Certification'],
    createdAt: new Date(),
    updatedAt: new Date()
  }
];

/**
 * Seed crops to both MongoDB and ElasticDB
 */
export const seedCrops = async (): Promise<void> => {
  try {
    // Clear existing crops from MongoDB
    await CropModel.deleteMany({});
    console.log('Existing crops cleared from MongoDB');

    // Insert crops into MongoDB
    await CropModel.insertMany(cropSeeds);
    console.log(`Seeded ${cropSeeds.length} crops to MongoDB`);

    // Initialize ElasticDB connection and seed crops
    await seedCropsToElasticDB();

    console.log(`Successfully seeded ${cropSeeds.length} crops to both MongoDB and ElasticDB`);
  } catch (error) {
    console.error('Error seeding crops:', error);
    throw error;
  }
};

/**
 * Seed crops to ElasticDB using the crop service reindexing functionality
 */
const seedCropsToElasticDB = async (): Promise<void> => {
  try {
    // Initialize ElasticDB connection
    const esConfig = {
      node: process.env['ELASTICSEARCH_NODE'] || 'https://befarma-f44523.es.us-east-1.aws.elastic.cloud:443',
      auth: {
        apiKey: process.env['ELASTICSEARCH_API_KEY']
      },
      serverMode: 'serverless' as const,
      index: process.env['ELASTICSEARCH_INDEX'] || 'befarma'
    };

    const esClient = await initElasticSearch(esConfig);
    console.log('ElasticDB connection initialized for crop seeding');

    // Get all crops from MongoDB (freshly seeded)
    const crops = await CropModel.find();

    if (crops.length === 0) {
      console.log('No crops found in MongoDB to seed to ElasticDB');
      return;
    }

    // Create index with mapping if it doesn't exist
    const indexName = esConfig.index;
    const indexExists = await esClient.indices.exists({ index: indexName });

    if (!indexExists) {
      await esClient.indices.create({
        index: indexName,
        settings: {
          number_of_shards: 3,
          number_of_replicas: 1,
          analysis: {
            analyzer: {
              crop_analyzer: {
                type: 'custom',
                tokenizer: 'standard',
                filter: ['lowercase', 'asciifolding']
              }
            }
          }
        }
      });
      console.log(`Created ElasticDB index: ${indexName}`);
    }

    // Bulk index all crops to ElasticDB
    const bulkBody: any[] = [];

    crops.forEach(crop => {
      const document = convertCropToElasticsearchDocument(crop);
      bulkBody.push(
        { index: { _index: indexName, _id: crop.cropId } },
        document
      );
    });

    await esClient.bulk({
      refresh: true,
      operations: bulkBody
    });

    console.log(`Successfully indexed ${crops.length} crops to ElasticDB`);
  } catch (error) {
    console.error('Error seeding crops to ElasticDB:', error);
    // Don't throw error to prevent MongoDB seeding from failing if ElasticDB is unavailable
    console.warn('Continuing with MongoDB seeding only...');
  }
};

/**
 * Convert MongoDB crop document to Elasticsearch document format
 * This is a simplified version of the conversion used in the crop service
 */
const convertCropToElasticsearchDocument = (crop: any): any => {
  return {
    id: crop._id.toString(),
    cropId: crop.cropId,
    numberOfPlots: crop.numberOfPlots,
    farmId: crop.farmId,
    sellerId: crop.sellerId,
    name: crop.name,
    type: crop.type,
    variety: crop.variety,
    plantingDate: crop.plantingDate.toISOString(),
    expectedHarvestDate: crop.expectedHarvestDate.toISOString(),
    actualHarvestDate: crop.actualHarvestDate?.toISOString(),
    growthStage: crop.growthStage,
    healthStatus: {
      status: crop.health.status,
      issues: crop.health.issues,
      lastCheck: crop.health.lastCheck.toISOString()
    },
    yield: crop.yield,
    resources: crop.resources,
    // Simplified seller info (will be populated with actual data in production)
    seller: {
      sellerId: crop.sellerId,
      name: `Farmer ${crop.sellerId}`,
      contact: '',
      email: '',
      location: {
        country: 'India',
        state: '',
        city: '',
        pincode: '',
        addressLine1: '',
        addressLine2: ''
      },
      certifications: []
    },
    // Simplified farm info (will be populated with actual data in production)
    farm: {
      farmId: crop.farmId,
      name: `Farm ${crop.farmId}`,
      location: {
        country: 'India',
        state: '',
        city: '',
        pincode: '',
        addressLine1: '',
        addressLine2: ''
      },
      totalArea: 0,
      soilType: crop.soilConditions?.type || '',
      waterSource: crop.waterAvailability || '',
      infrastructure: [],
      certifications: crop.certifications || []
    },
    // Product information for marketplace
    product: {
      isForSale: true,
      price: {
        amount: 0,
        currency: 'INR',
        unit: 'kg',
        negotiable: true
      },
      availability: {
        status: 'AVAILABLE',
        quantity: crop.yield?.expected || 0,
        unit: crop.yield?.unit || 'kg'
      },
      delivery: {
        methods: ['PICKUP', 'DELIVERY'],
        charges: 0,
        estimatedTime: '1-2 days',
        radius: 50
      },
      qualityGrade: 'STANDARD',
      packaging: {
        type: 'Standard',
        sizes: ['1kg', '5kg', '10kg'],
        customPackaging: false
      }
    },
    // Crop attributes for search and filtering
    attributes: {
      images: crop.images || [],
      tags: crop.tags || [],
      cropCategory: crop.metadata?.cropCategory || crop.type,
      farmingMethod: crop.metadata?.farmingMethod || 'Conventional',
      irrigationMethod: crop.metadata?.irrigationMethod || 'Traditional',
      harvestSeason: crop.metadata?.harvestSeason || 'All Season',
      pestDiseaseStatus: crop.metadata?.pestDiseaseStatus || 'None',
      storageMethod: crop.metadata?.storageMethod || 'Standard',
      nutrientManagement: crop.metadata?.nutrientManagement || 'Standard',
      waterSource: crop.metadata?.waterSource || crop.waterAvailability || 'Unknown',
      pesticideUsage: crop.metadata?.pesticideUsage || 'Standard',
      seedType: crop.metadata?.seedType || 'Standard',
      harvestingMethod: crop.metadata?.harvestingMethod || 'Manual',
      soilConditions: crop.soilConditions || {},
      waterAvailability: crop.waterAvailability || '',
      nutritionalInfo: crop.nutritionalInfo || {},
      cultivation: {
        irrigationNeeds: crop.cultivation?.irrigationNeeds || 'Moderate',
        fertilizerRequirements: crop.cultivation?.fertilizerRequirements || 'Moderate',
        pestControl: crop.cultivation?.pestControl || 'Standard',
        climateConditions: crop.cultivation?.climateConditions || 'Moderate',
        sowingMethod: 'Direct Seeding',
        spacingRequirements: 'Standard',
        companionCrops: []
      },
      postHarvest: crop.postHarvest || {},
      sustainability: crop.sustainability || {}
    },
    maintenance: {
      schedule: {
        irrigation: crop.maintenance?.schedule?.irrigation?.map((date: Date) => date.toISOString()) || [],
        fertilization: crop.maintenance?.schedule?.fertilization?.map((date: Date) => date.toISOString()) || [],
        pestControl: crop.maintenance?.schedule?.pestControl?.map((date: Date) => date.toISOString()) || [],
        inspection: crop.maintenance?.schedule?.inspection?.map((date: Date) => date.toISOString()) || []
      },
      history: {
        activities: crop.maintenance?.history?.activities?.map((activity: any) => ({
          ...activity,
          date: activity.date.toISOString()
        })) || []
      }
    },
    weather: {
      forecasts: crop.weather?.forecasts?.map((forecast: any) => ({
        ...forecast,
        date: forecast.date.toISOString()
      })) || [],
      alerts: crop.weather?.alerts?.map((alert: any) => ({
        ...alert,
        startDate: alert.startDate.toISOString(),
        endDate: alert.endDate.toISOString()
      })) || []
    },
    certifications: crop.certifications || [],
    createdAt: crop.createdAt.toISOString(),
    updatedAt: crop.updatedAt.toISOString()
  };
};