import { SellerDocument, SellerModel } from '../../schemas/mongo/seller.schema';
import { MongoDBConnector } from '../../mongo.connector';
import { Types } from 'mongoose';

/**
 * Repository for seller-related database operations
 */
export class SellerRepository {
  private connector: MongoDBConnector;
  
  /**
   * Creates a new seller repository
   * @param connector MongoDB connector instance
   */
  constructor(connector: MongoDBConnector) {
    this.connector = connector;
  }
  
  /**
   * Find a seller by ID
   * @param sellerId The seller ID to find
   * @returns The seller document or null if not found
   */
  async findById(sellerId: string): Promise<SellerDocument | null> {
    try {
      return await SellerModel.findOne({ sellerId }).exec();
    } catch (error) {
      console.error('Error finding seller by ID:', error);
      throw error;
    }
  }
  
  /**
   * Find a seller by email
   * @param email The email to find
   * @returns The seller document or null if not found
   */
  async findByEmail(email: string): Promise<SellerDocument | null> {
    try {
      return await SellerModel.findOne({ 'personalInfo.email': email }).exec();
    } catch (error) {
      console.error('Error finding seller by email:', error);
      throw error;
    }
  }

  /**
   * Find a seller by email with password (for authentication)
   * @param email The email to find
   * @returns The seller document with password or null if not found
   */
  async findByEmailWithPassword(email: string): Promise<SellerDocument | null> {
    try {
      return await SellerModel.findOne({ 'personalInfo.email': email }).select('+password').exec();
    } catch (error) {
      console.error('Error finding seller by email with password:', error);
      throw error;
    }
  }
  
  /**
   * Find sellers by status
   * @param status The status to filter by
   * @returns Array of seller documents
   */
  async findByStatus(status: string): Promise<SellerDocument[]> {
    try {
      return await SellerModel.find({ status }).exec();
    } catch (error) {
      console.error('Error finding sellers by status:', error);
      throw error;
    }
  }
  
  /**
   * Create a new seller
   * @param sellerData The seller data to create
   * @returns The created seller document
   */
  async create(sellerData: Partial<SellerDocument>): Promise<SellerDocument> {
    try {
      const seller = new SellerModel(sellerData);
      return await seller.save();
    } catch (error) {
      console.error('Error creating seller:', error);
      throw error;
    }
  }
  
  /**
   * Update a seller by ID
   * @param sellerId The seller ID to update
   * @param updateData The data to update
   * @returns The updated seller document or null if not found
   */
  async update(sellerId: string, updateData: Partial<SellerDocument>): Promise<SellerDocument | null> {
    try {
      return await SellerModel.findOneAndUpdate(
        { sellerId },
        { $set: updateData },
        { new: true }
      ).exec();
    } catch (error) {
      console.error('Error updating seller:', error);
      throw error;
    }
  }
  
  /**
   * Delete a seller by ID
   * @param sellerId The seller ID to delete
   * @returns True if deleted, false if not found
   */
  async delete(sellerId: string): Promise<boolean> {
    try {
      const result = await SellerModel.deleteOne({ sellerId }).exec();
      return result.deletedCount > 0;
    } catch (error) {
      console.error('Error deleting seller:', error);
      throw error;
    }
  }
  
  /**
   * Find all sellers with pagination
   * @param page Page number (1-based)
   * @param limit Number of items per page
   * @returns Object with sellers array and pagination info
   */
  async findAll(page: number = 1, limit: number = 10): Promise<{ sellers: SellerDocument[]; total: number; pages: number }> {
    try {
      const skip = (page - 1) * limit;
      const [sellers, total] = await Promise.all([
        SellerModel.find().skip(skip).limit(limit).exec(),
        SellerModel.countDocuments(),
      ]);
      
      return {
        sellers,
        total,
        pages: Math.ceil(total / limit),
      };
    } catch (error) {
      console.error('Error finding all sellers:', error);
      throw error;
    }
  }
  
  /**
   * Add a farm to a seller
   * @param sellerId The seller ID
   * @param farmId The farm ID to add
   * @returns The updated seller document or null if not found
   */
  async addFarm(sellerId: string, farmId: Types.ObjectId | string): Promise<SellerDocument | null> {
    try {
      return await SellerModel.findOneAndUpdate(
        { sellerId },
        { $addToSet: { farms: farmId } },
        { new: true }
      ).exec();
    } catch (error) {
      console.error('Error adding farm to seller:', error);
      throw error;
    }
  }
  
  /**
   * Remove a farm from a seller
   * @param sellerId The seller ID
   * @param farmId The farm ID to remove
   * @returns The updated seller document or null if not found
   */
  async removeFarm(sellerId: string, farmId: Types.ObjectId | string): Promise<SellerDocument | null> {
    try {
      return await SellerModel.findOneAndUpdate(
        { sellerId },
        { $pull: { farms: farmId } },
        { new: true }
      ).exec();
    } catch (error) {
      console.error('Error removing farm from seller:', error);
      throw error;
    }
  }
  
  /**
   * Update seller verification status
   * @param sellerId The seller ID
   * @param status The new verification status
   * @returns The updated seller document or null if not found
   */
  async updateVerificationStatus(
    sellerId: string,
    status: 'PENDING' | 'VERIFIED' | 'REJECTED'
  ): Promise<SellerDocument | null> {
    try {
      return await SellerModel.findOneAndUpdate(
        { sellerId },
        { $set: { verificationStatus: status } },
        { new: true }
      ).exec();
    } catch (error) {
      console.error('Error updating seller verification status:', error);
      throw error;
    }
  }
} 