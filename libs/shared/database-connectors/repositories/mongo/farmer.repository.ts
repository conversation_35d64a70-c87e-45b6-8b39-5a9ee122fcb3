import { FarmerDocument, FarmerModel } from '../../schemas/mongo/farmer.schema';
import { MongoDBConnector } from '../../mongo.connector';
import { Types } from 'mongoose';

/**
 * Repository for farmer-related database operations
 */
export class FarmerRepository {
  private connector: MongoDBConnector;
  
  /**
   * Creates a new farmer repository
   * @param connector MongoDB connector instance
   */
  constructor(connector: MongoDBConnector) {
    this.connector = connector;
  }
  
  /**
   * Find a farmer by ID
   * @param farmerId The farmer ID to find
   * @returns The farmer document or null if not found
   */
  async findById(farmerId: string): Promise<FarmerDocument | null> {
    try {
      return await FarmerModel.findOne({ farmerId }).exec();
    } catch (error) {
      console.error('Error finding farmer by ID:', error);
      throw error;
    }
  }
  
  /**
   * Find a farmer by email
   * @param email The email to find
   * @returns The farmer document or null if not found
   */
  async findByEmail(email: string): Promise<FarmerDocument | null> {
    try {
      return await FarmerModel.findOne({ 'personalInfo.contact.email': email }).exec();
    } catch (error) {
      console.error('Error finding farmer by email:', error);
      throw error;
    }
  }
  
  /**
   * Find farmers by account status
   * @param status The status to filter by
   * @returns Array of farmer documents
   */
  async findByAccountStatus(status: 'ACTIVE' | 'SUSPENDED' | 'BLOCKED'): Promise<FarmerDocument[]> {
    try {
      return await FarmerModel.find({ 'status.accountStatus': status }).exec();
    } catch (error) {
      console.error('Error finding farmers by account status:', error);
      throw error;
    }
  }
  
  /**
   * Find farmers by verification status
   * @param status The verification status to filter by
   * @returns Array of farmer documents
   */
  async findByVerificationStatus(status: 'PENDING' | 'VERIFIED' | 'REJECTED'): Promise<FarmerDocument[]> {
    try {
      return await FarmerModel.find({ 'status.verificationStatus': status }).exec();
    } catch (error) {
      console.error('Error finding farmers by verification status:', error);
      throw error;
    }
  }
  
  /**
   * Create a new farmer
   * @param farmerData The farmer data to create
   * @returns The created farmer document
   */
  async create(farmerData: Partial<FarmerDocument>): Promise<FarmerDocument> {
    try {
      const farmer = new FarmerModel(farmerData);
      return await farmer.save();
    } catch (error) {
      console.error('Error creating farmer:', error);
      throw error;
    }
  }
  
  /**
   * Update a farmer by ID
   * @param farmerId The farmer ID to update
   * @param updateData The data to update
   * @returns The updated farmer document or null if not found
   */
  async update(farmerId: string, updateData: Partial<FarmerDocument>): Promise<FarmerDocument | null> {
    try {
      return await FarmerModel.findOneAndUpdate(
        { farmerId },
        { $set: updateData },
        { new: true }
      ).exec();
    } catch (error) {
      console.error('Error updating farmer:', error);
      throw error;
    }
  }
  
  /**
   * Delete a farmer by ID
   * @param farmerId The farmer ID to delete
   * @returns True if deleted, false if not found
   */
  async delete(farmerId: string): Promise<boolean> {
    try {
      const result = await FarmerModel.deleteOne({ farmerId }).exec();
      return result.deletedCount > 0;
    } catch (error) {
      console.error('Error deleting farmer:', error);
      throw error;
    }
  }
  
  /**
   * Find all farmers with pagination
   * @param page Page number (1-based)
   * @param limit Number of items per page
   * @returns Object with farmers array and pagination info
   */
  async findAll(page: number = 1, limit: number = 10): Promise<{ farmers: FarmerDocument[]; total: number; pages: number }> {
    try {
      const skip = (page - 1) * limit;
      const [farmers, total] = await Promise.all([
        FarmerModel.find().skip(skip).limit(limit).exec(),
        FarmerModel.countDocuments(),
      ]);
      
      return {
        farmers,
        total,
        pages: Math.ceil(total / limit),
      };
    } catch (error) {
      console.error('Error finding all farmers:', error);
      throw error;
    }
  }
  
  /**
   * Add a farm to a farmer
   * @param farmerId The farmer ID
   * @param farmId The farm ID to add
   * @returns The updated farmer document or null if not found
   */
  async addFarm(farmerId: string, farmId: Types.ObjectId | string): Promise<FarmerDocument | null> {
    try {
      return await FarmerModel.findOneAndUpdate(
        { farmerId },
        { $addToSet: { farms: farmId } },
        { new: true }
      ).exec();
    } catch (error) {
      console.error('Error adding farm to farmer:', error);
      throw error;
    }
  }
  
  /**
   * Remove a farm from a farmer
   * @param farmerId The farmer ID
   * @param farmId The farm ID to remove
   * @returns The updated farmer document or null if not found
   */
  async removeFarm(farmerId: string, farmId: Types.ObjectId | string): Promise<FarmerDocument | null> {
    try {
      return await FarmerModel.findOneAndUpdate(
        { farmerId },
        { $pull: { farms: farmId } },
        { new: true }
      ).exec();
    } catch (error) {
      console.error('Error removing farm from farmer:', error);
      throw error;
    }
  }
  
  /**
   * Update farmer verification status
   * @param farmerId The farmer ID
   * @param status The new verification status
   * @returns The updated farmer document or null if not found
   */
  async updateVerificationStatus(
    farmerId: string,
    status: 'PENDING' | 'VERIFIED' | 'REJECTED'
  ): Promise<FarmerDocument | null> {
    try {
      return await FarmerModel.findOneAndUpdate(
        { farmerId },
        { $set: { 'status.verificationStatus': status } },
        { new: true }
      ).exec();
    } catch (error) {
      console.error('Error updating farmer verification status:', error);
      throw error;
    }
  }
  
  /**
   * Update farmer account status
   * @param farmerId The farmer ID
   * @param status The new account status
   * @returns The updated farmer document or null if not found
   */
  async updateAccountStatus(
    farmerId: string,
    status: 'ACTIVE' | 'SUSPENDED' | 'BLOCKED'
  ): Promise<FarmerDocument | null> {
    try {
      return await FarmerModel.findOneAndUpdate(
        { farmerId },
        { $set: { 'status.accountStatus': status } },
        { new: true }
      ).exec();
    } catch (error) {
      console.error('Error updating farmer account status:', error);
      throw error;
    }
  }
  
  /**
   * Add a payment to farmer's history
   * @param farmerId The farmer ID
   * @param payment The payment details
   * @returns The updated farmer document or null if not found
   */
  async addPayment(
    farmerId: string,
    payment: {
      transactionId: string;
      amount: number;
      date?: Date;
      status?: 'PENDING' | 'COMPLETED' | 'FAILED';
    }
  ): Promise<FarmerDocument | null> {
    try {
      const paymentData = {
        ...payment,
        date: payment.date || new Date(),
        status: payment.status || 'PENDING',
      };
      
      return await FarmerModel.findOneAndUpdate(
        { farmerId },
        { 
          $push: { 'financials.paymentHistory': paymentData },
          $inc: { 'financials.totalEarnings': payment.status === 'COMPLETED' ? payment.amount : 0 }
        },
        { new: true }
      ).exec();
    } catch (error) {
      console.error('Error adding payment to farmer:', error);
      throw error;
    }
  }
  
  /**
   * Add a communication message to farmer's history
   * @param farmerId The farmer ID
   * @param message The message details
   * @returns The updated farmer document or null if not found
   */
  async addCommunicationMessage(
    farmerId: string,
    message: {
      messageId: string;
      type: string;
      content: string;
      timestamp?: Date;
      status?: 'SENT' | 'DELIVERED' | 'READ';
    }
  ): Promise<FarmerDocument | null> {
    try {
      const messageData = {
        ...message,
        timestamp: message.timestamp || new Date(),
        status: message.status || 'SENT',
      };
      
      return await FarmerModel.findOneAndUpdate(
        { farmerId },
        { $push: { 'communication.history': messageData } },
        { new: true }
      ).exec();
    } catch (error) {
      console.error('Error adding communication message to farmer:', error);
      throw error;
    }
  }
  
  /**
   * Add an audit trail entry to farmer
   * @param farmerId The farmer ID
   * @param auditEntry The audit entry details
   * @returns The updated farmer document or null if not found
   */
  async addAuditTrail(
    farmerId: string,
    auditEntry: {
      actionId: string;
      action: string;
      adminId: string;
      timestamp?: Date;
      details: string;
    }
  ): Promise<FarmerDocument | null> {
    try {
      const auditData = {
        ...auditEntry,
        timestamp: auditEntry.timestamp || new Date(),
      };
      
      return await FarmerModel.findOneAndUpdate(
        { farmerId },
        { $push: { auditTrail: auditData } },
        { new: true }
      ).exec();
    } catch (error) {
      console.error('Error adding audit trail to farmer:', error);
      throw error;
    }
  }
} 