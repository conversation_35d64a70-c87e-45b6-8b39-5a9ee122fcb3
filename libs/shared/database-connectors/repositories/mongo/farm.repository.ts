import { FarmDocument, FarmModel } from '../../schemas/mongo/farm.schema';
import { MongoDBConnector } from '../../mongo.connector';
import { Types } from 'mongoose';

/**
 * Repository for farm-related database operations
 */
export class FarmRepository {
  private connector: MongoDBConnector;
  
  /**
   * Creates a new farm repository
   * @param connector MongoDB connector instance
   */
  constructor(connector: MongoDBConnector) {
    this.connector = connector;
  }
  
  /**
   * Find a farm by ID
   * @param farmId The farm ID to find
   * @returns The farm document or null if not found
   */
  async findById(farmId: string): Promise<FarmDocument | null> {
    try {
      return await FarmModel.findOne({ farmId }).exec();
    } catch (error) {
      console.error('Error finding farm by ID:', error);
      throw error;
    }
  }
  
  /**
   * Find farms by seller ID
   * @param sellerId The seller ID to find farms for
   * @returns Array of farm documents
   */
  async findBySellerId(sellerId: string): Promise<FarmDocument[]> {
    try {
      return await FarmModel.find({ sellerId }).exec();
    } catch (error) {
      console.error('Error finding farms by seller ID:', error);
      throw error;
    }
  }
  
  /**
   * Find farms by status
   * @param status The status to filter by
   * @returns Array of farm documents
   */
  async findByStatus(status: string): Promise<FarmDocument[]> {
    try {
      return await FarmModel.find({ status }).exec();
    } catch (error) {
      console.error('Error finding farms by status:', error);
      throw error;
    }
  }
  
  /**
   * Create a new farm
   * @param farmData The farm data to create
   * @returns The created farm document
   */
  async create(farmData: Partial<FarmDocument>): Promise<FarmDocument> {
    try {
      const farm = new FarmModel(farmData);
      return await farm.save();
    } catch (error) {
      console.error('Error creating farm:', error);
      throw error;
    }
  }
  
  /**
   * Update a farm by ID
   * @param farmId The farm ID to update
   * @param updateData The data to update
   * @returns The updated farm document or null if not found
   */
  async update(farmId: string, updateData: Partial<FarmDocument>): Promise<FarmDocument | null> {
    try {
      return await FarmModel.findOneAndUpdate(
        { farmId },
        { $set: updateData },
        { new: true }
      ).exec();
    } catch (error) {
      console.error('Error updating farm:', error);
      throw error;
    }
  }
  
  /**
   * Delete a farm by ID
   * @param farmId The farm ID to delete
   * @returns True if deleted, false if not found
   */
  async delete(farmId: string): Promise<boolean> {
    try {
      const result = await FarmModel.deleteOne({ farmId }).exec();
      return result.deletedCount > 0;
    } catch (error) {
      console.error('Error deleting farm:', error);
      throw error;
    }
  }
  
  /**
   * Find all farms with pagination
   * @param page Page number (1-based)
   * @param limit Number of items per page
   * @returns Object with farms array and pagination info
   */
  async findAll(page: number = 1, limit: number = 10): Promise<{ farms: FarmDocument[]; total: number; pages: number }> {
    try {
      const skip = (page - 1) * limit;
      const [farms, total] = await Promise.all([
        FarmModel.find().skip(skip).limit(limit).exec(),
        FarmModel.countDocuments(),
      ]);
      
      return {
        farms,
        total,
        pages: Math.ceil(total / limit),
      };
    } catch (error) {
      console.error('Error finding all farms:', error);
      throw error;
    }
  }
  
  /**
   * Add a plot to a farm
   * @param farmId The farm ID
   * @param plotId The plot ID to add
   * @returns The updated farm document or null if not found
   */
  async addPlot(farmId: string, plotId: Types.ObjectId | string): Promise<FarmDocument | null> {
    try {
      return await FarmModel.findOneAndUpdate(
        { farmId },
        { $addToSet: { plots: plotId } },
        { new: true }
      ).exec();
    } catch (error) {
      console.error('Error adding plot to farm:', error);
      throw error;
    }
  }
  
  /**
   * Remove a plot from a farm
   * @param farmId The farm ID
   * @param plotId The plot ID to remove
   * @returns The updated farm document or null if not found
   */
  async removePlot(farmId: string, plotId: Types.ObjectId | string): Promise<FarmDocument | null> {
    try {
      return await FarmModel.findOneAndUpdate(
        { farmId },
        { $pull: { plots: plotId } },
        { new: true }
      ).exec();
    } catch (error) {
      console.error('Error removing plot from farm:', error);
      throw error;
    }
  }
  
  /**
   * Update farm status
   * @param farmId The farm ID
   * @param status The new status
   * @returns The updated farm document or null if not found
   */
  async updateStatus(
    farmId: string,
    status: 'ACTIVE' | 'INACTIVE'
  ): Promise<FarmDocument | null> {
    try {
      return await FarmModel.findOneAndUpdate(
        { farmId },
        { $set: { status } },
        { new: true }
      ).exec();
    } catch (error) {
      console.error('Error updating farm status:', error);
      throw error;
    }
  }
} 