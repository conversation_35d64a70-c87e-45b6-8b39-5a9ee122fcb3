import mongoose, { Schema, Document } from 'mongoose';

/**
 * Farmer document interface
 */
export interface FarmerDocument extends Document {
  farmerId: string;
  personalInfo: {
    name: string;
    contact: {
      phone: string;
      email: string;
      address: {
        country: string;
        state: string;
        city: string;
        pincode: string;
        addressLine1: string;
        addressLine2: string;
      };
    };
    identification: {
      idType: 'PASSPORT' | 'NATIONAL_ID' | 'DRIVERS_LICENSE';
      idNumber: string;
      idExpiry: Date;
    };
    bankingInfo: {
      accountNumber: string;
      bankName: string;
      branchCode: string;
    };
  };
  farmingInfo: {
    experience: number;
    specializations: string[];
    certifications: [{
      certId: string;
      type: string;
      issuer: string;
      issueDate: Date;
      expiryDate: Date;
    }];
    training: [{
      trainingId: string;
      name: string;
      completionDate: Date;
      status: 'COMPLETED' | 'IN_PROGRESS' | 'PENDING';
    }];
  };
  status: {
    accountStatus: 'ACTIVE' | 'SUSPENDED' | 'BLOCKED';
    verificationStatus: 'PENDING' | 'VERIFIED' | 'REJECTED';
    rating: number;
    performance: {
      successRate: number;
      responseTime: number;
      customerSatisfaction: number;
    };
  };
  farms: mongoose.Types.ObjectId[];
  financials: {
    totalEarnings: number;
    pendingPayments: number;
    paymentHistory: [{
      transactionId: string;
      amount: number;
      date: Date;
      status: 'PENDING' | 'COMPLETED' | 'FAILED';
    }];
  };
  communication: {
    preferences: {
      notifications: boolean;
      language: string;
      contactMethod: 'EMAIL' | 'SMS' | 'PUSH';
    };
    history: [{
      messageId: string;
      type: string;
      content: string;
      timestamp: Date;
      status: 'SENT' | 'DELIVERED' | 'READ';
    }];
  };
  auditTrail: [{
    actionId: string;
    action: string;
    adminId: string;
    timestamp: Date;
    details: string;
  }];
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Farmer schema definition
 */
const FarmerSchema = new Schema(
  {
    farmerId: {
      type: String,
      required: true,
      unique: true,
    },
    personalInfo: {
      name: {
        type: String,
        required: true,
      },
      contact: {
        phone: {
          type: String,
          required: true,
        },
        email: {
          type: String,
          required: true,
          lowercase: true,
        },
        address: {
          country: {
            type: String,
            required: true,
            default: 'INDIA'
          },
          state: {
            type: String,
            required: true,
          },
          city: {
            type: String,
            required: true,
          },
          pincode: {
            type: String,
            required: true,
          },
          addressLine1: {
            type: String,
            required: true,
          },
          addressLine2: {
            type: String,
            required: true,
          },
        },
      },
      identification: {
        idType: {
          type: String,
          enum: ['PASSPORT', 'NATIONAL_ID', 'DRIVERS_LICENSE'],
          required: true,
        },
        idNumber: {
          type: String,
          required: true,
        },
        idExpiry: {
          type: Date,
        },
      },
      bankingInfo: {
        accountNumber: {
          type: String,
        },
        bankName: {
          type: String,
        },
        branchCode: {
          type: String,
        },
      },
    },
    farmingInfo: {
      experience: {
        type: Number,
        default: 0,
      },
      specializations: [{
        type: String,
      }],
      certifications: [{
        certId: {
          type: String,
        },
        type: {
          type: String,
        },
        issuer: {
          type: String,
        },
        issueDate: {
          type: Date,
        },
        expiryDate: {
          type: Date,
        },
      }],
      training: [{
        trainingId: {
          type: String,
        },
        name: {
          type: String,
        },
        completionDate: {
          type: Date,
        },
        status: {
          type: String,
          enum: ['COMPLETED', 'IN_PROGRESS', 'PENDING'],
          default: 'PENDING',
        },
      }],
    },
    status: {
      accountStatus: {
        type: String,
        enum: ['ACTIVE', 'SUSPENDED', 'BLOCKED'],
        default: 'ACTIVE',
      },
      verificationStatus: {
        type: String,
        enum: ['PENDING', 'VERIFIED', 'REJECTED'],
        default: 'PENDING',
      },
      rating: {
        type: Number,
        default: 0,
        min: 0,
        max: 5,
      },
      performance: {
        successRate: {
          type: Number,
          default: 0,
        },
        responseTime: {
          type: Number,
          default: 0,
        },
        customerSatisfaction: {
          type: Number,
          default: 0,
        },
      },
    },
    farms: [{
      type: Schema.Types.ObjectId,
      ref: 'Farm',
    }],
    financials: {
      totalEarnings: {
        type: Number,
        default: 0,
      },
      pendingPayments: {
        type: Number,
        default: 0,
      },
      paymentHistory: [{
        transactionId: {
          type: String,
        },
        amount: {
          type: Number,
        },
        date: {
          type: Date,
        },
        status: {
          type: String,
          enum: ['PENDING', 'COMPLETED', 'FAILED'],
          default: 'PENDING',
        },
      }],
    },
    communication: {
      preferences: {
        notifications: {
          type: Boolean,
          default: true,
        },
        language: {
          type: String,
          default: 'en',
        },
        contactMethod: {
          type: String,
          enum: ['EMAIL', 'SMS', 'PUSH'],
          default: 'EMAIL',
        },
      },
      history: [{
        messageId: {
          type: String,
        },
        type: {
          type: String,
        },
        content: {
          type: String,
        },
        timestamp: {
          type: Date,
          default: Date.now,
        },
        status: {
          type: String,
          enum: ['SENT', 'DELIVERED', 'READ'],
          default: 'SENT',
        },
      }],
    },
    auditTrail: [{
      actionId: {
        type: String,
      },
      action: {
        type: String,
      },
      adminId: {
        type: String,
      },
      timestamp: {
        type: Date,
        default: Date.now,
      },
      details: {
        type: String,
      },
    }],
  },
  {
    timestamps: true,
  }
);

// Create and export the Farmer model
export const FarmerModel = mongoose.model<FarmerDocument>('Farmer', FarmerSchema); 