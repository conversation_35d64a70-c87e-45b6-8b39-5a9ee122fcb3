import mongoose, { Schema, Document } from 'mongoose';

/**
 * Farmer document interface
 * This represents farmers who sell their produce
 */
export interface FarmerDocument extends Document {
  farmerId: string; // Using format 'FARMER-XXX' to identify the farmer
  personalInfo: {
    name: string;
    contact: string;
    email: string;
    address: {
      country: string;
      state: string;
      city: string;
      pincode: string;
      addressLine1: string;
      addressLine2: string;
    };
  };
  documents: {
    identityProof: string;
    landOwnership: string;
    certifications: string[];
  };
  bankDetails: {
    accountNumber: string;
    bankName: string;
    ifscCode: string;
  };
  status: 'PENDING' | 'ACTIVE' | 'SUSPENDED';
  verificationStatus: 'PENDING' | 'VERIFIED' | 'REJECTED';
  farms: mongoose.Types.ObjectId[];
  password: string; // Bcrypted password for authentication
  createdAt: Date;
  updatedAt: Date;
  statusHistory: {
    status: 'PENDING' | 'ACTIVE' | 'SUSPENDED' | 'REJECTED' | 'VERIFIED';
    updatedBy: string;
    reason?: string;
    updatedAt: Date;
  }[];
}

/**
 * Farmer schema definition
 * This schema represents farmers who sell their produce
 */
const FarmerSchema = new Schema(
  {
    farmerId: {
      type: String,
      required: true,
      unique: true,
    },
    personalInfo: {
      name: {
        type: String,
        required: true,
      },
      contact: {
        type: String,
        required: true,
      },
      email: {
        type: String,
        required: true,
        lowercase: true,
      },
      address: {
        country: {
          type: String,
          required: true,
          default: 'INDIA'
        },
        state: {
          type: String,
          required: true,
        },
        city: {
          type: String,
          required: true,
        },
        pincode: {
          type: String,
          required: true,
        },
        addressLine1: {
          type: String,
          required: true,
        },
        addressLine2: {
          type: String,
          required: true,
        },
      },
    },
    documents: {
      identityProof: {
        type: String,
      },
      landOwnership: {
        type: String,
      },
      certifications: [
        {
          type: String,
        },
      ],
    },
    bankDetails: {
      accountNumber: {
        type: String,
      },
      bankName: {
        type: String,
      },
      ifscCode: {
        type: String,
      },
    },
    status: {
      type: String,
      enum: ['PENDING', 'ACTIVE', 'SUSPENDED'],
      default: 'PENDING',
    },
    verificationStatus: {
      type: String,
      enum: ['PENDING', 'VERIFIED', 'REJECTED'],
      default: 'PENDING',
    },
    statusHistory: [
      {
        status: {
          type: String,
          enum: ['PENDING', 'ACTIVE', 'SUSPENDED', 'REJECTED', 'VERIFIED'],
          required: true,
        },
        updatedBy: {
          type: String,
          required: true,
        },
        reason: {
          type: String,
        },
        updatedAt: {
          type: Date,
          default: Date.now,
        },
      },
    ],
    farms: [
      {
        type: Schema.Types.ObjectId,
        ref: 'Farm',
      },
    ],
    password: {
      type: String,
      required: true,
      select: false, // Don't include password in queries by default
    },
  },
  {
    timestamps: true,
  }
);

// Create and export the Farmer model
export const FarmerModel = mongoose.model<FarmerDocument>('Farmer', FarmerSchema);

// For backward compatibility, also export as SellerModel and SellerDocument
export const SellerModel = FarmerModel;
export type SellerDocument = FarmerDocument;
