import { Schema, model, Document } from 'mongoose';

/**
 * Crop growth stage enum
 */
export enum CropGrowthStage {
  PLANTING = 'PLANTING',
  GROWING = 'GROWING',
  MATURING = 'MATURING',
  READY = 'READY',
}

/**
 * Crop health status enum
 */
export enum CropHealthStatus {
  HEALTHY = 'HEALTHY',
  WARNING = 'WARNING',
  CRITICAL = 'CRITICAL',
}

/**
 * Crop document interface for MongoDB
 */
export interface ICrop extends Document {
  cropId: string;
  farmId: string;
  sellerId: string;
  numberOfPlots: number;
  name: string;
  type: string;
  variety: string;
  plantingDate: Date;
  expectedHarvestDate: Date;
  actualHarvestDate?: Date;
  growthStage: CropGrowthStage;
  health: {
    status: CropHealthStatus;
    issues: string[];
    lastCheck: Date;
  };
  yield: {
    expected: number;
    actual: number;
    unit: string;
  };
  resources: {
    water: number;
    fertilizer: number;
    pesticides: number;
  };
  soilConditions: {
    type: string;
    ph: number;
    nutrients: string[];
  };
  waterAvailability: string;
  metadata: {
    cropCategory: string;
    farmingMethod: string;
    irrigationMethod: string;
    harvestSeason: string;
    pestDiseaseStatus: string;
    storageMethod: string;
    nutrientManagement: string;
    waterSource: string;
    pesticideUsage: string;
    seedType: string;
    harvestingMethod: string;
  };
  nutritionalInfo?: {
    calories?: number;
    protein?: number;
    carbs?: number;
    fiber?: number;
    vitamins?: string[];
  };
  cultivation: {
    irrigationNeeds: string;
    fertilizerRequirements: string;
    pestControl: string;
    climateConditions: string;
  };
  postHarvest?: {
    storageRequirements: string;
    shelfLife: string;
    processingMethods: string[];
  };
  sustainability?: {
    waterUsage: string;
    carbonFootprint: string;
    pesticide: string;
  };
  maintenance: {
    schedule: {
      irrigation: Date[];
      fertilization: Date[];
      pestControl: Date[];
      inspection: Date[];
    };
    history: {
      activities: [{
        type: string;
        date: Date;
        description: string;
        performedBy: string;
      }];
    };
  };
  weather: {
    forecasts: [{
      date: Date;
      temperature: {
        min: number;
        max: number;
        unit: string;
      };
      precipitation: {
        amount: number;
        unit: string;
        type: string;
      };
      humidity: number;
      windSpeed: {
        value: number;
        unit: string;
      };
    }];
    alerts: [{
      type: string;
      severity: string;
      description: string;
      startDate: Date;
      endDate: Date;
    }];
  };
  images: string[];
  tags: string[];
  certifications: string[];
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Crop schema for MongoDB
 */
const CropSchema = new Schema<ICrop>(
  {
    cropId: { type: String, required: true, unique: true },
    farmId: { type: String, required: true, index: true },
    sellerId: { type: String, required: true, index: true },
    numberOfPlots: { type: Number, required: true, min: 1 },
    name: { type: String, required: true },
    type: { type: String, required: true },
    variety: { type: String, required: true },
    plantingDate: { type: Date, required: true },
    expectedHarvestDate: { type: Date, required: true },
    actualHarvestDate: { type: Date },
    growthStage: { 
      type: String, 
      enum: Object.values(CropGrowthStage),
      default: CropGrowthStage.PLANTING,
      required: true 
    },
    health: {
      status: { 
        type: String, 
        enum: Object.values(CropHealthStatus), 
        default: CropHealthStatus.HEALTHY 
      },
      issues: [{ type: String }],
      lastCheck: { type: Date, default: Date.now }
    },
    yield: {
      expected: { type: Number, required: true },
      actual: { type: Number, default: 0 },
      unit: { type: String, required: true }
    },
    resources: {
      water: { type: Number, default: 0 },
      fertilizer: { type: Number, default: 0 },
      pesticides: { type: Number, default: 0 }
    },
    soilConditions: {
      type: { type: String, required: true },
      ph: { type: Number, required: true },
      nutrients: [{ type: String }]
    },
    waterAvailability: { type: String, required: true },
    metadata: {
      cropCategory: { type: String, required: true },
      farmingMethod: { type: String, required: true },
      irrigationMethod: { type: String, required: true },
      harvestSeason: { type: String, required: true },
      pestDiseaseStatus: { type: String, default: 'None' },
      storageMethod: { type: String },
      nutrientManagement: { type: String },
      waterSource: { type: String, required: true },
      pesticideUsage: { type: String, default: 'None' },
      seedType: { type: String, required: true },
      harvestingMethod: { type: String }
    },
    nutritionalInfo: {
      calories: { type: Number },
      protein: { type: Number },
      carbs: { type: Number },
      fiber: { type: Number },
      vitamins: [{ type: String }]
    },
    cultivation: {
      irrigationNeeds: { type: String, required: true },
      fertilizerRequirements: { type: String, required: true },
      pestControl: { type: String, required: true },
      climateConditions: { type: String, required: true }
    },
    postHarvest: {
      storageRequirements: { type: String },
      shelfLife: { type: String },
      processingMethods: [{ type: String }]
    },
    sustainability: {
      waterUsage: { type: String },
      carbonFootprint: { type: String },
      pesticide: { type: String }
    },
    maintenance: {
      schedule: {
        irrigation: [{ type: Date }],
        fertilization: [{ type: Date }],
        pestControl: [{ type: Date }],
        inspection: [{ type: Date }]
      },
      history: {
        activities: [{
          type: { type: String, required: true },
          date: { type: Date, required: true },
          description: { type: String, required: true },
          performedBy: { type: String, required: true }
        }]
      }
    },
    weather: {
      forecasts: [{
        date: { type: Date, required: true },
        temperature: {
          min: { type: Number, required: true },
          max: { type: Number, required: true },
          unit: { type: String, required: true, default: 'Celsius' }
        },
        precipitation: {
          amount: { type: Number, required: true },
          unit: { type: String, required: true, default: 'mm' },
          type: { type: String, required: true }
        },
        humidity: { type: Number, required: true },
        windSpeed: {
          value: { type: Number, required: true },
          unit: { type: String, required: true, default: 'km/h' }
        }
      }],
      alerts: [{
        type: { type: String, required: true },
        severity: { type: String, required: true },
        description: { type: String, required: true },
        startDate: { type: Date, required: true },
        endDate: { type: Date, required: true }
      }]
    },
    images: [{ type: String }],
    tags: [{ type: String }],
    certifications: [{ type: String }]
  },
  {
    timestamps: true,
    versionKey: false
  }
);

// Compound index for farm+crop combination
CropSchema.index({ farmId: 1, cropId: 1 }, { unique: true });

// Index for faster queries by growth stage and health status
CropSchema.index({ growthStage: 1, 'health.status': 1 });

// Index for crop harvesting queries
CropSchema.index({ expectedHarvestDate: 1 });

// Text search index
CropSchema.index({ name: 'text', type: 'text', variety: 'text', 'metadata.cropCategory': 'text' });

/**
 * Crop model export if used directly
 */
export const CropModel = model<ICrop>('Crop', CropSchema);

/**
 * Export the schema for use in other modules
 */
export default CropSchema; 