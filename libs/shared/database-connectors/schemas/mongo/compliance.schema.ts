import mongoose, { Schema, Document } from 'mongoose';

/**
 * Compliance document interface
 */
export interface ComplianceDocument extends Document {
  complianceId: string;
  type: 'SELLER' | 'FARM' | 'PLOT' | 'TRANSACTION';
  status: 'COMPLIANT' | 'NON_COMPLIANT' | 'PENDING';
  requirements: [{
    requirementId: string;
    description: string;
    status: 'MET' | 'NOT_MET' | 'PENDING';
    dueDate: Date;
  }];
  violations: [{
    violationId: string;
    type: string;
    severity: 'LOW' | 'MEDIUM' | 'HIGH';
    status: 'OPEN' | 'RESOLVED';
    resolution: string;
  }];
  auditTrail: [{
    actionId: string;
    action: string;
    timestamp: Date;
    adminId: string;
  }];
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Compliance schema definition
 */
const ComplianceSchema = new Schema(
  {
    complianceId: {
      type: String,
      required: true,
      unique: true,
    },
    type: {
      type: String,
      enum: ['SELLER', 'FARM', 'PLOT', 'TRANSACTION'],
      required: true,
    },
    status: {
      type: String,
      enum: ['COMPLIANT', 'NON_COMPLIANT', 'PENDING'],
      default: 'PENDING',
    },
    requirements: [{
      requirementId: {
        type: String,
        required: true,
      },
      description: {
        type: String,
        required: true,
      },
      status: {
        type: String,
        enum: ['MET', 'NOT_MET', 'PENDING'],
        default: 'PENDING',
      },
      dueDate: {
        type: Date,
      },
    }],
    violations: [{
      violationId: {
        type: String,
      },
      type: {
        type: String,
      },
      severity: {
        type: String,
        enum: ['LOW', 'MEDIUM', 'HIGH'],
      },
      status: {
        type: String,
        enum: ['OPEN', 'RESOLVED'],
        default: 'OPEN',
      },
      resolution: {
        type: String,
      },
    }],
    auditTrail: [{
      actionId: {
        type: String,
      },
      action: {
        type: String,
      },
      timestamp: {
        type: Date,
        default: Date.now,
      },
      adminId: {
        type: String,
      },
    }],
  },
  {
    timestamps: true,
  }
);

// Create and export the Compliance model
export const ComplianceModel = mongoose.model<ComplianceDocument>('Compliance', ComplianceSchema); 