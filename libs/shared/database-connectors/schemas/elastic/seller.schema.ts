/**
 * Elasticsearch schema for seller documents
 * Optimized for search and discovery of farmers/sellers
 */

export interface SellerDocument {
  id: string;
  sellerId: string;
  personalInfo: {
    name: string;
    contact: string;
    email: string;
    address: {
      country: string;
      state: string;
      city: string;
      pincode: string;
      addressLine1: string;
      addressLine2: string;
    };
  };
  status: 'PENDING' | 'ACTIVE' | 'SUSPENDED';
  verificationStatus: 'PENDING' | 'VERIFIED' | 'REJECTED';
  
  // Enhanced seller information for search
  farmingProfile: {
    experienceYears: number;
    specializations: string[];
    farmingMethods: ('ORGANIC' | 'CONVENTIONAL' | 'INTEGRATED')[];
    certifications: {
      type: string;
      issuer: string;
      issueDate: string;
      expiryDate: string;
    }[];
    totalFarms: number;
    totalArea: number;
    sustainabilityScore: number;
  };
  
  // Denormalized farm information for better search
  farms: {
    farmId: string;
    name: string;
    location: {
      country: string;
      state: string;
      city: string;
      pincode: string;
      coordinates?: {
        latitude: number;
        longitude: number;
      };
    };
    totalArea: number;
    soilType: string;
    waterSource: string;
    farmingMethod: 'ORGANIC' | 'CONVENTIONAL' | 'INTEGRATED';
    currentCrops: string[];
    certifications: string[];
  }[];
  
  // Current crop portfolio for search
  cropPortfolio: {
    totalCrops: number;
    activeCrops: number;
    cropTypes: string[];
    cropVarieties: string[];
    seasonalBreakdown: {
      kharif: string[];
      rabi: string[];
      zaid: string[];
    };
    organicCrops: number;
    conventionalCrops: number;
  };
  
  // Business metrics for ranking
  businessMetrics: {
    totalOrders: number;
    totalRevenue: number;
    averageRating: number;
    totalReviews: number;
    responseTime: number; // in hours
    fulfillmentRate: number; // percentage
    popularityScore: number;
    reliabilityScore: number;
  };
  
  // Search and discovery metadata
  searchMetadata: {
    tags: string[];
    keywords: string[];
    categories: string[];
    regions: string[];
    languages: string[];
    availability: {
      status: 'AVAILABLE' | 'BUSY' | 'UNAVAILABLE';
      nextAvailableDate?: string;
    };
    lastActiveDate: string;
    joinDate: string;
  };
  
  createdAt: string; // ISO date string
  updatedAt: string; // ISO date string
}

/**
 * Seller mapping for Elasticsearch
 */
export const sellerMapping = {
  mappings: {
    properties: {
      id: { type: 'keyword' },
      sellerId: { type: 'keyword' },
      personalInfo: {
        properties: {
          name: { 
            type: 'text',
            analyzer: 'seller_analyzer',
            fields: {
              keyword: { type: 'keyword' },
              suggest: {
                type: 'completion',
                analyzer: 'simple'
              }
            }
          },
          contact: { type: 'keyword' },
          email: { type: 'keyword' },
          address: {
            properties: {
              country: { type: 'keyword' },
              state: { type: 'keyword' },
              city: { type: 'keyword' },
              pincode: { type: 'keyword' },
              addressLine1: { type: 'text' },
              addressLine2: { type: 'text' }
            }
          }
        }
      },
      status: { type: 'keyword' },
      verificationStatus: { type: 'keyword' },
      
      farmingProfile: {
        properties: {
          experienceYears: { type: 'integer' },
          specializations: { type: 'keyword' },
          farmingMethods: { type: 'keyword' },
          certifications: {
            type: 'nested',
            properties: {
              type: { type: 'keyword' },
              issuer: { type: 'keyword' },
              issueDate: { type: 'date' },
              expiryDate: { type: 'date' }
            }
          },
          totalFarms: { type: 'integer' },
          totalArea: { type: 'float' },
          sustainabilityScore: { type: 'float' }
        }
      },
      
      farms: {
        type: 'nested',
        properties: {
          farmId: { type: 'keyword' },
          name: { 
            type: 'text',
            analyzer: 'seller_analyzer',
            fields: { keyword: { type: 'keyword' } }
          },
          location: {
            properties: {
              country: { type: 'keyword' },
              state: { type: 'keyword' },
              city: { type: 'keyword' },
              pincode: { type: 'keyword' },
              coordinates: {
                type: 'geo_point'
              }
            }
          },
          totalArea: { type: 'float' },
          soilType: { type: 'keyword' },
          waterSource: { type: 'keyword' },
          farmingMethod: { type: 'keyword' },
          currentCrops: { type: 'keyword' },
          certifications: { type: 'keyword' }
        }
      },
      
      cropPortfolio: {
        properties: {
          totalCrops: { type: 'integer' },
          activeCrops: { type: 'integer' },
          cropTypes: { type: 'keyword' },
          cropVarieties: { type: 'keyword' },
          seasonalBreakdown: {
            properties: {
              kharif: { type: 'keyword' },
              rabi: { type: 'keyword' },
              zaid: { type: 'keyword' }
            }
          },
          organicCrops: { type: 'integer' },
          conventionalCrops: { type: 'integer' }
        }
      },
      
      businessMetrics: {
        properties: {
          totalOrders: { type: 'integer' },
          totalRevenue: { type: 'float' },
          averageRating: { type: 'float' },
          totalReviews: { type: 'integer' },
          responseTime: { type: 'float' },
          fulfillmentRate: { type: 'float' },
          popularityScore: { type: 'float' },
          reliabilityScore: { type: 'float' }
        }
      },
      
      searchMetadata: {
        properties: {
          tags: { type: 'keyword' },
          keywords: { type: 'keyword' },
          categories: { type: 'keyword' },
          regions: { type: 'keyword' },
          languages: { type: 'keyword' },
          availability: {
            properties: {
              status: { type: 'keyword' },
              nextAvailableDate: { type: 'date' }
            }
          },
          lastActiveDate: { type: 'date' },
          joinDate: { type: 'date' }
        }
      },
      
      createdAt: { type: 'date' },
      updatedAt: { type: 'date' }
    }
  }
};

/**
 * Create index configuration for sellers
 */
export const sellerIndexConfig = {
  settings: {
    number_of_shards: 3,
    number_of_replicas: 1,
    analysis: {
      analyzer: {
        seller_analyzer: {
          type: 'custom',
          tokenizer: 'standard',
          filter: ['lowercase', 'asciifolding', 'stop']
        }
      }
    }
  },
  mappings: sellerMapping.mappings
};
