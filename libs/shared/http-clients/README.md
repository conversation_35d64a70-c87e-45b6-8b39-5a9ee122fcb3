# HTTP Clients for AgriTech Microservices

This library provides properly configured axios instances and API service classes for making HTTP requests to all AgriTech microservices.

## Features

- ✅ **Pre-configured axios instances** for all microservices
- ✅ **Automatic authentication** with JWT tokens
- ✅ **Request/Response interceptors** for logging and error handling
- ✅ **Retry logic** with exponential backoff
- ✅ **Circuit breaker pattern** for resilience
- ✅ **TypeScript support** with proper type definitions
- ✅ **Centralized error handling** with custom error classes
- ✅ **Token management** with automatic refresh

## Quick Start

### 1. Basic Usage

```typescript
import { apiServices, apiUtils } from '@shared/http-clients';

// Login and set authentication token
const loginResult = await apiUtils.login('<EMAIL>', 'password');
console.log('Logged in:', loginResult.user);

// Use API services
const sellers = await apiServices.seller.getAllSellers();
const farms = await apiServices.farm.getAllFarms();
const crops = await apiServices.crop.getAllCrops();
```

### 2. Individual Service Usage

```typescript
import { SellerApiService, FarmApiService } from '@shared/http-clients';

const sellerApi = new SellerApiService();
const farmApi = new FarmApiService();

// Get seller details
const seller = await sellerApi.getSellerById('seller-123');

// Create a new farm
const newFarm = await farmApi.createFarm({
  name: 'Green Valley Farm',
  location: { latitude: 12.34, longitude: 56.78 },
  size: 10.5,
  sellerId: 'seller-123'
});
```

### 3. Authentication

```typescript
import { authApi, adminApi, apiUtils } from '@shared/http-clients';

// Regular user login
const userAuth = await authApi.login('<EMAIL>', 'password');

// Admin login
const adminAuth = await adminApi.login('<EMAIL>', 'password');

// Check authentication status
if (apiUtils.isAuthenticated()) {
  console.log('User is authenticated');
}

// Logout
await apiUtils.logout();
```

## API Services

### AuthApiService
```typescript
import { authApi } from '@shared/http-clients';

// Login
const auth = await authApi.login('<EMAIL>', 'password');

// Register
const newUser = await authApi.register({
  email: '<EMAIL>',
  password: 'password',
  name: 'John Doe'
});

// Refresh token
const refreshed = await authApi.refreshToken('refresh_token_here');

// Logout
await authApi.logout();
```

### SellerApiService
```typescript
import { sellerApi } from '@shared/http-clients';

// Get all sellers
const sellers = await sellerApi.getAllSellers({ page: 1, limit: 10 });

// Get seller by ID
const seller = await sellerApi.getSellerById('seller-123');

// Create seller
const newSeller = await sellerApi.createSeller({
  name: 'John Farmer',
  email: '<EMAIL>',
  phone: '+1234567890'
});

// Update seller
const updated = await sellerApi.updateSeller('seller-123', {
  name: 'John Updated Farmer'
});

// Upload document
const formData = new FormData();
formData.append('document', file);
formData.append('type', 'kyc');
const uploaded = await sellerApi.uploadDocument('seller-123', formData);

// Verify seller
await sellerApi.verifySeller('seller-123');
```

### FarmApiService
```typescript
import { farmApi } from '@shared/http-clients';

// Get all farms
const farms = await farmApi.getAllFarms({ sellerId: 'seller-123' });

// Create farm
const newFarm = await farmApi.createFarm({
  name: 'Green Valley Farm',
  location: { latitude: 12.34, longitude: 56.78 },
  size: 10.5,
  sellerId: 'seller-123'
});

// Search farms
const searchResults = await farmApi.searchFarms({
  q: 'organic',
  location: { lat: 12.34, lon: 56.78, radius: 50 }
});
```

### CropApiService
```typescript
import { cropApi } from '@shared/http-clients';

// Get all crops
const crops = await cropApi.getAllCrops({ category: 'vegetables' });

// Create crop
const newCrop = await cropApi.createCrop({
  name: 'Organic Tomatoes',
  type: 'vegetable',
  variety: 'cherry',
  farmId: 'farm-123'
});

// Search crops
const searchResults = await cropApi.searchCrops({
  q: 'tomato',
  filters: { organic: true, available: true }
});

// Get suggestions
const suggestions = await cropApi.getCropSuggestions('tom');
```

### AdminApiService
```typescript
import { adminApi } from '@shared/http-clients';

// Admin login
const adminAuth = await adminApi.login('<EMAIL>', 'password');

// Get dashboard
const dashboard = await adminApi.getDashboard();

// System health
const health = await adminApi.getSystemHealth();

// Manage sellers
const sellers = await adminApi.manageSellers('list');
const seller = await adminApi.manageSellers('get', 'seller-123');
await adminApi.manageSellers('verify', 'seller-123', { verified: true });
```

### AnalyticsApiService
```typescript
import { analyticsApi } from '@shared/http-clients';

// Dashboard analytics
const dashboard = await analyticsApi.getDashboard();

// Sales analytics
const sales = await analyticsApi.getSalesAnalytics({
  startDate: '2024-01-01',
  endDate: '2024-12-31'
});

// Generate report
const report = await analyticsApi.generateReport({
  type: 'sales',
  period: 'monthly',
  format: 'pdf'
});

// Market insights
const insights = await analyticsApi.getMarketInsights({
  region: 'north',
  crop: 'wheat'
});
```

### NotificationApiService
```typescript
import { notificationApi } from '@shared/http-clients';

// Get notifications
const notifications = await notificationApi.getNotifications({ unread: true });

// Send notification
await notificationApi.sendNotification({
  to: 'user-123',
  title: 'New Order',
  message: 'You have a new order!',
  type: 'order'
});

// Mark as read
await notificationApi.markAsRead('notification-123');

// Bulk notifications
await notificationApi.sendBulkNotifications({
  recipients: ['user-1', 'user-2'],
  template: 'welcome',
  data: { name: 'John' }
});
```

### OrderApiService
```typescript
import { orderApi } from '@shared/http-clients';

// Get orders
const orders = await orderApi.getAllOrders({ status: 'pending' });

// Create order
const newOrder = await orderApi.createOrder({
  cropId: 'crop-123',
  quantity: 100,
  buyerId: 'buyer-123'
});

// Process payment
await orderApi.processPayment('order-123', {
  method: 'card',
  amount: 1000,
  currency: 'USD'
});

// Track order
const tracking = await orderApi.getOrderTracking('order-123');
```

## Configuration

### Environment Variables

```bash
# Service URLs
SELLER_SERVICE_URL=http://localhost:3001/api/v1/sellers
ADMIN_SERVICE_URL=http://localhost:3002/api/v1/admin
ANALYTICS_SERVICE_URL=http://localhost:3003/api/analytics
CROP_SERVICE_URL=http://localhost:3004/api/crops
FARM_SERVICE_URL=http://localhost:3005/api/farms
NOTIFICATION_SERVICE_URL=http://localhost:3008/api/notifications
ORDER_SERVICE_URL=http://localhost:3009/api/orders

# Optional: Enable/disable logging
NODE_ENV=development  # Enables logging in development
```

### Custom Configuration

```typescript
import { createApiClient, ApiClients } from '@shared/http-clients';

// Create custom client
const customClient = createApiClient({
  baseURL: 'https://api.example.com',
  timeout: 60000,
  retries: 5,
  enableAuth: true,
  enableLogging: true
});

// Set custom token
ApiClients.setAuthToken('your-jwt-token');
```

## Error Handling

```typescript
import { ApiError } from '@shared/http-clients';

try {
  const seller = await sellerApi.getSellerById('invalid-id');
} catch (error) {
  if (error instanceof ApiError) {
    console.error('API Error:', error.status, error.message);
    console.error('Error data:', error.data);
  } else {
    console.error('Unknown error:', error);
  }
}
```

## Advanced Usage

### Custom Interceptors

```typescript
import { ApiClients } from '@shared/http-clients';

const client = ApiClients.getSellerService();

// Add request interceptor
client.interceptors.request.use(
  (config) => {
    config.headers['X-Custom-Header'] = 'custom-value';
    return config;
  },
  (error) => Promise.reject(error)
);

// Add response interceptor
client.interceptors.response.use(
  (response) => {
    console.log('Response received:', response.status);
    return response;
  },
  (error) => {
    console.error('Response error:', error.response?.status);
    return Promise.reject(error);
  }
);
```

### File Uploads

```typescript
import { sellerApi } from '@shared/http-clients';

// Upload document
const formData = new FormData();
formData.append('document', file);
formData.append('type', 'kyc');
formData.append('description', 'KYC Document');

const result = await sellerApi.uploadDocument('seller-123', formData);
```

## Testing

```typescript
import { apiServices, apiUtils } from '@shared/http-clients';

// Mock authentication for testing
apiUtils.setAuthToken('mock-jwt-token');

// Test API calls
const sellers = await apiServices.seller.getAllSellers();
expect(sellers).toBeDefined();
```

## Best Practices

1. **Always handle errors** with try-catch blocks
2. **Use environment variables** for service URLs
3. **Set authentication tokens** after login
4. **Clear tokens** on logout
5. **Use TypeScript** for better type safety
6. **Enable logging** in development
7. **Configure timeouts** appropriately for each service
8. **Implement retry logic** for critical operations

## Support

For issues or questions:
1. Check the service documentation
2. Verify environment variables
3. Test individual endpoints
4. Check network connectivity
5. Review error logs
