import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse, AxiosError } from 'axios';

/**
 * Base configuration for all axios instances
 */
export interface ApiClientConfig {
  baseURL: string;
  timeout?: number;
  retries?: number;
  retryDelay?: number;
  enableAuth?: boolean;
  enableLogging?: boolean;
}

/**
 * Authentication token storage
 */
class TokenManager {
  private static instance: TokenManager;
  private tokens: Map<string, string> = new Map();

  static getInstance(): TokenManager {
    if (!TokenManager.instance) {
      TokenManager.instance = new TokenManager();
    }
    return TokenManager.instance;
  }

  setToken(service: string, token: string): void {
    this.tokens.set(service, token);
  }

  getToken(service: string): string | undefined {
    return this.tokens.get(service);
  }

  removeToken(service: string): void {
    this.tokens.delete(service);
  }

  clearAllTokens(): void {
    this.tokens.clear();
  }
}

/**
 * Create a configured axios instance
 */
export function createApiClient(config: ApiClientConfig): AxiosInstance {
  const instance = axios.create({
    baseURL: config.baseURL,
    timeout: config.timeout || 30000,
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    },
  });

  // Request interceptor for authentication and logging
  instance.interceptors.request.use(
    (requestConfig) => {
      // Add authentication token if enabled
      if (config.enableAuth) {
        const tokenManager = TokenManager.getInstance();
        const serviceName = extractServiceName(config.baseURL);
        const token = tokenManager.getToken(serviceName) || tokenManager.getToken('default');
        
        if (token) {
          requestConfig.headers.Authorization = `Bearer ${token}`;
        }
      }

      // Add request ID for tracing
      requestConfig.headers['X-Request-ID'] = generateRequestId();

      // Log request if enabled
      if (config.enableLogging) {
        console.log(`[API Request] ${requestConfig.method?.toUpperCase()} ${requestConfig.url}`, {
          headers: requestConfig.headers,
          data: requestConfig.data,
        });
      }

      return requestConfig;
    },
    (error) => {
      if (config.enableLogging) {
        console.error('[API Request Error]', error);
      }
      return Promise.reject(error);
    }
  );

  // Response interceptor for error handling and logging
  instance.interceptors.response.use(
    (response: AxiosResponse) => {
      // Log successful response if enabled
      if (config.enableLogging) {
        console.log(`[API Response] ${response.status} ${response.config.url}`, {
          data: response.data,
          headers: response.headers,
        });
      }
      return response;
    },
    async (error: AxiosError) => {
      const originalRequest = error.config as AxiosRequestConfig & { _retry?: boolean };

      // Log error if enabled
      if (config.enableLogging) {
        console.error(`[API Error] ${error.response?.status} ${error.config?.url}`, {
          message: error.message,
          response: error.response?.data,
        });
      }

      // Handle 401 Unauthorized - token refresh logic
      if (error.response?.status === 401 && !originalRequest._retry) {
        originalRequest._retry = true;
        
        // Clear invalid token
        const tokenManager = TokenManager.getInstance();
        const serviceName = extractServiceName(config.baseURL);
        tokenManager.removeToken(serviceName);
        
        // You can implement token refresh logic here
        // For now, we'll just reject the promise
      }

      // Retry logic for network errors or 5xx errors
      if (shouldRetry(error) && config.retries && config.retries > 0) {
        return retryRequest(instance, originalRequest, config.retries, config.retryDelay || 1000);
      }

      return Promise.reject(error);
    }
  );

  return instance;
}

/**
 * Extract service name from base URL
 */
function extractServiceName(baseURL: string): string {
  const url = new URL(baseURL);
  const pathSegments = url.pathname.split('/').filter(Boolean);
  return pathSegments[pathSegments.length - 1] || 'default';
}

/**
 * Generate unique request ID
 */
function generateRequestId(): string {
  return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * Check if request should be retried
 */
function shouldRetry(error: AxiosError): boolean {
  if (!error.response) {
    // Network error
    return true;
  }
  
  const status = error.response.status;
  // Retry on 5xx errors and 429 (rate limit)
  return status >= 500 || status === 429;
}

/**
 * Retry failed request
 */
async function retryRequest(
  instance: AxiosInstance,
  config: AxiosRequestConfig,
  retries: number,
  delay: number
): Promise<AxiosResponse> {
  for (let i = 0; i < retries; i++) {
    try {
      await new Promise(resolve => setTimeout(resolve, delay * Math.pow(2, i))); // Exponential backoff
      return await instance.request(config);
    } catch (error) {
      if (i === retries - 1) {
        throw error;
      }
    }
  }
  throw new Error('Max retries exceeded');
}

/**
 * Service-specific axios instances
 */
export class ApiClients {
  private static instances: Map<string, AxiosInstance> = new Map();



  static getSellerService(): AxiosInstance {
    if (!this.instances.has('seller')) {
      this.instances.set('seller', createApiClient({
        baseURL: process.env.SELLER_SERVICE_URL || 'http://localhost:3001/api/v1/sellers',
        timeout: 30000,
        retries: 3,
        enableAuth: true,
        enableLogging: process.env.NODE_ENV === 'development',
      }));
    }
    return this.instances.get('seller')!;
  }

  static getAdminService(): AxiosInstance {
    if (!this.instances.has('admin')) {
      this.instances.set('admin', createApiClient({
        baseURL: process.env.ADMIN_SERVICE_URL || 'http://localhost:3002/api/v1/admin',
        timeout: 30000,
        retries: 3,
        enableAuth: true,
        enableLogging: process.env.NODE_ENV === 'development',
      }));
    }
    return this.instances.get('admin')!;
  }

  static getAnalyticsService(): AxiosInstance {
    if (!this.instances.has('analytics')) {
      this.instances.set('analytics', createApiClient({
        baseURL: process.env.ANALYTICS_SERVICE_URL || 'http://localhost:3003/api/analytics',
        timeout: 45000, // Analytics might need more time
        retries: 3,
        enableAuth: true,
        enableLogging: process.env.NODE_ENV === 'development',
      }));
    }
    return this.instances.get('analytics')!;
  }

  static getCropService(): AxiosInstance {
    if (!this.instances.has('crop')) {
      this.instances.set('crop', createApiClient({
        baseURL: process.env.CROP_SERVICE_URL || 'http://localhost:3004/api/crops',
        timeout: 30000,
        retries: 3,
        enableAuth: true,
        enableLogging: process.env.NODE_ENV === 'development',
      }));
    }
    return this.instances.get('crop')!;
  }

  static getFarmService(): AxiosInstance {
    if (!this.instances.has('farm')) {
      this.instances.set('farm', createApiClient({
        baseURL: process.env.FARM_SERVICE_URL || 'http://localhost:3005/api/farms',
        timeout: 30000,
        retries: 3,
        enableAuth: true,
        enableLogging: process.env.NODE_ENV === 'development',
      }));
    }
    return this.instances.get('farm')!;
  }

  static getNotificationService(): AxiosInstance {
    if (!this.instances.has('notification')) {
      this.instances.set('notification', createApiClient({
        baseURL: process.env.NOTIFICATION_SERVICE_URL || 'http://localhost:3008/api/notifications',
        timeout: 15000, // Notifications should be faster
        retries: 2,
        enableAuth: true,
        enableLogging: process.env.NODE_ENV === 'development',
      }));
    }
    return this.instances.get('notification')!;
  }

  static getOrderService(): AxiosInstance {
    if (!this.instances.has('order')) {
      this.instances.set('order', createApiClient({
        baseURL: process.env.ORDER_SERVICE_URL || 'http://localhost:3009/api/orders',
        timeout: 30000,
        retries: 3,
        enableAuth: true,
        enableLogging: process.env.NODE_ENV === 'development',
      }));
    }
    return this.instances.get('order')!;
  }

  // Utility method to set authentication token for all services
  static setAuthToken(token: string, service?: string): void {
    const tokenManager = TokenManager.getInstance();
    if (service) {
      tokenManager.setToken(service, token);
    } else {
      tokenManager.setToken('default', token);
    }
  }

  // Utility method to clear authentication tokens
  static clearAuthTokens(): void {
    const tokenManager = TokenManager.getInstance();
    tokenManager.clearAllTokens();
  }
}

export { TokenManager };
