# AgriTech Seller Backend Project Structure

This document outlines the structure of the AgriTech Seller Backend microservices architecture.

## Root Directory Structure

```
seller_backend/
├── apidocs/           # API documentation for different services
├── apps/              # Main microservices applications
├── libs/              # Shared libraries and utilities
├── .nx/               # NX workspace configuration and cache
└── configuration files
```

## Microservices (apps/)

### Admin Service

```
admin-service/
└── src/
    ├── assets/        # Static assets
    └── controllers/   # Admin controllers
```

### Analytics Service

```
analytics-service/
└── src/
    ├── assets/        # Static assets
    └── models/        # Data models
```

### API Gateway

```
api-gateway/
└── src/
    ├── api/
    │   ├── middlewares/   # API middleware functions
    │   ├── routes/        # Route definitions
    │   └── types/         # TypeScript type definitions
    ├── assets/            # Static assets
    ├── config/
    │   └── db/           # Database configurations
    ├── models/           # Data models
    └── utils/            # Utility functions
```

### Crop Service

```
crop-service/
└── src/
    ├── assets/           # Static assets
    ├── config/
    │   └── db/          # Database configurations
    ├── controllers/     # Route controllers
    ├── dto/            # Data Transfer Objects
    ├── middleware/     # Service middleware
    ├── models/         # Data models
    ├── routes/         # Route definitions
    └── services/       # Business logic services
```

### Farm Service

```
farm-service/
└── src/
    ├── assets/          # Static assets
    ├── config/
    │   └── db/         # Database configurations
    ├── controllers/    # Route controllers
    ├── dto/           # Data Transfer Objects
    ├── middleware/    # Service middleware
    ├── models/        # Data models
    ├── routes/        # Route definitions
    └── services/      # Business logic services
```

### Notification Service

```
notification-service/
└── src/
    ├── assets/         # Static assets
    └── models/         # Data models
```

### Order Service

```
order-service/
└── src/
    ├── assets/         # Static assets
    └── models/         # Data models
```

### Seller Service

```
seller-service/
└── src/
    ├── assets/         # Static assets
    ├── config/
    │   └── db/        # Database configurations
    ├── controllers/   # Route controllers
    ├── dto/          # Data Transfer Objects
    ├── middleware/   # Service middleware
    ├── models/       # Data models
    ├── routes/       # Route definitions
    └── services/     # Business logic services
```

## Shared Libraries (libs/)

### Feature Libraries

```
feature/
└── auth/             # Authentication library
    └── src/
        └── lib/      # Authentication implementation
```

### Shared Libraries

```
shared/
├── database-connectors/
│   ├── interfaces/   # Database interfaces
│   ├── repositories/ # Database repositories
│   │   ├── elastic/ # Elasticsearch repositories
│   │   └── mongo/   # MongoDB repositories
│   ├── schemas/     # Database schemas
│   │   ├── elastic/ # Elasticsearch schemas
│   │   └── mongo/   # MongoDB schemas
│   ├── scripts/     # Database scripts
│   ├── seeds/       # Seed data
│   └── services/    # Database services
└── utils/
    └── middleware/  # Shared middleware utilities
```

## API Documentation (apidocs/)

```
apidocs/
├── common/          # Common API documentation
└── services/        # Service-specific documentation
    ├── crop/       # Crop service documentation
    ├── farm/       # Farm service documentation
    └── seller/     # Seller service documentation
```

## Configuration Files

- `.cursorignore` - Cursor IDE ignore file
- `.gitignore` - Git ignore file
- `.prettierignore` - Prettier ignore file
- `.prettierrc` - Prettier configuration
- `nx.json` - NX workspace configuration
- `package.json` - Project dependencies and scripts
- `tsconfig.base.json` - Base TypeScript configuration
- `setup_env.sh` - Environment setup script
- `test-seed-api.sh` - API seeding test script
- `SERVICE_PORTS.md` - Service ports documentation
- `seller-admin-architecture.md` - Admin architecture documentation
- `seller-farm-management-architecture.md` - Farm management architecture documentation
