# AgriTech Seller Backend - Project Briefing Document

## Executive Summary

The AgriTech Seller Backend is a comprehensive microservices-based platform designed to manage agricultural operations, focusing on farmer-sellers, farm management, crop monitoring, and agricultural commerce. The system enables farmers to register as sellers, manage their farms and plots, monitor crop growth, and participate in agricultural trade.

## Project Overview

**Project Name:** AgriTech Seller Backend
**Version:** 1.0.0
**Architecture:** Microservices with API Gateway
**Primary Technology Stack:** Node.js, Express, TypeScript, MongoDB, Elasticsearch
**Development Framework:** Nx Monorepo

## Business Domain

This platform serves the agricultural technology sector, specifically targeting:

- **Primary Users:** Farmers who act as sellers of agricultural produce
- **Secondary Users:** Buyers, administrators, and agricultural service providers
- **Core Business Model:** Agricultural marketplace connecting farmers with buyers
- **Geographic Focus:** India (with default country setting)

## System Architecture

### Microservices Structure

The system consists of 11 core microservices:

| Service                        | Port | Purpose                                             |
| ------------------------------ | ---- | --------------------------------------------------- |
| **API Gateway**          | 3000 | Central entry point, routing, authentication        |
| **Seller Service**       | 3001 | Farmer registration, profile management, onboarding |
| **Admin Service**        | 3002 | Administrative functions and management             |
| **Analytics Service**    | 3003 | Data analytics and reporting                        |
| **Crop Service**         | 3004 | Crop lifecycle management and monitoring            |
| **Farm Service**         | 3005 | Farm registration and management                    |
| **Financial Service**    | 3006 | Payment processing and financial transactions       |
| **Inventory Service**    | 3007 | Stock and inventory management                      |
| **Notification Service** | 3008 | Communication and alerts                            |
| **Order Service**        | 3009 | Order processing and management                     |
| **Plot Service**         | 3010 | Land plot management and tracking                   |

### Technology Stack

**Backend Framework:**

- Node.js with Express.js
- TypeScript for type safety
- Nx monorepo for project organization

**Databases:**

- MongoDB for primary data storage
- Elasticsearch for search and analytics

**Security & Middleware:**

- JWT authentication
- bcrypt for password hashing
- Rate limiting with express-rate-limit
- CORS support
- Helmet for security headers

**Infrastructure:**

- Circuit breaker pattern implementation
- Connection pooling for HTTP requests
- Optimized logging and monitoring

## Core Functionality

### 1. Seller/Farmer Management

- **Registration Process:** Multi-step farmer onboarding
- **Document Verification:** Identity proof, land ownership, certifications
- **Profile Management:** Personal information, contact details, address
- **Bank Details:** Account information for payments
- **Status Management:** Pending, Active, Suspended states
- **Verification Workflow:** Pending, Verified, Rejected statuses

### 2. Farm Management

- **Farm Registration:** Location, area, soil type, water source
- **Infrastructure Tracking:** Equipment and facility management
- **Certification Management:** Organic and other agricultural certifications
- **GPS Coordination:** Location tracking with latitude/longitude
- **Multi-Plot Support:** Farms can contain multiple plots

### 3. Crop Management

- **Comprehensive Crop Tracking:**
  - Growth stages (Planting, Growing, Maturing, Ready)
  - Health monitoring (Healthy, Warning, Critical)
  - Yield tracking (expected vs actual)
- **Resource Management:** Water, fertilizer, pesticide usage
- **Soil Conditions:** pH levels, nutrient tracking
- **Weather Integration:** Forecasts, alerts, and climate data
- **Maintenance Scheduling:** Irrigation, fertilization, pest control
- **Nutritional Information:** Calories, protein, vitamins tracking
- **Sustainability Metrics:** Water usage, carbon footprint

### 4. Order Processing

- **Order Lifecycle:** Pending → Confirmed → Processing → Completed
- **Payment Integration:** Multiple payment methods support
- **Transaction Tracking:** Payment status and transaction IDs
- **Communication:** Notifications and messaging system

### 5. Authentication & Security

- **JWT-based Authentication:** Secure token management
- **Role-based Access Control:** Different permission levels
- **Rate Limiting:** Protection against API abuse
- **Input Validation:** Comprehensive request validation
- **Circuit Breaker:** Service resilience patterns

## Data Models

### Key Entities

**Seller (Farmer):**

- Unique seller ID (FARMER-XXX format)
- Personal information and contact details
- Address with full location hierarchy
- Document management (identity, land ownership)
- Bank account details
- Farm associations

**Farm:**

- Unique farm ID
- Owner seller reference
- Complete location data with coordinates
- Physical attributes (area, soil type, water source)
- Infrastructure and certifications
- Associated plots

**Crop:**

- Detailed crop lifecycle tracking
- Growth stage and health monitoring
- Resource utilization metrics
- Weather data integration
- Maintenance history
- Sustainability tracking

**Order:**

- Complete order workflow
- Payment processing integration
- Timeline tracking
- Communication logs

## Current Development Status

### Implemented Features

✅ **Core Infrastructure:** Microservices architecture with API Gateway
✅ **Authentication System:** JWT-based auth with role management
✅ **Seller Onboarding:** Registration, verification, and activation
✅ **Data Models:** Comprehensive MongoDB schemas
✅ **API Documentation:** Swagger integration across services
✅ **Development Tools:** Environment setup and build scripts

### Architecture Patterns

✅ **Circuit Breaker Pattern:** Service resilience
✅ **Rate Limiting:** API protection
✅ **Connection Pooling:** Performance optimization
✅ **Centralized Logging:** Request/response tracking
✅ **Input Validation:** Request validation middleware

## Technical Specifications

### Development Environment

- **Node.js:** Latest LTS version
- **Package Manager:** npm with package-lock.json
- **Monorepo:** Nx workspace configuration
- **TypeScript:** Version 5.7.2
- **Build System:** Webpack with esbuild

### Database Configuration

- **Primary Database:** MongoDB with Mongoose ODM
- **Search Engine:** Elasticsearch for analytics
- **Connection Management:** Pooled connections for performance

### API Design

- **RESTful APIs:** Following REST principles
- **Consistent Response Format:** Standardized error and success responses
- **Swagger Documentation:** Available at `/api/docs` for each service
- **Versioning Strategy:** Path-based API versioning

## Development Process

### Scripts Available

- `npm start`: Run core services (API Gateway, Seller Service, Admin Service)
- `npm run serve:all`: Start all microservices
- `npm run build:all`: Build all services
- Environment setup via `setup_env.sh`
- Swagger documentation setup via `setup-swagger.sh`

### Code Organization

- **Apps Directory:** Individual microservices
- **Libs Directory:** Shared libraries and utilities
- **Shared Database Connectors:** Reusable database schemas and repositories
- **Common Utilities:** Authentication, validation, middleware

## Quality Assurance

### Security Measures

- JWT token authentication
- Password hashing with bcrypt
- Rate limiting protection
- CORS configuration
- Helmet security headers
- Input sanitization and validation

### Performance Optimizations

- Connection pooling for HTTP requests
- Circuit breaker pattern for service resilience
- Optimized database queries with indexing
- Compressed response handling

## Future Considerations

### Scalability Readiness

- Microservices architecture supports horizontal scaling
- Database sharding preparation with proper indexing
- API Gateway ready for load balancing
- Service mesh preparation with circuit breakers

### Integration Points

- Payment gateway integration framework
- Third-party weather API integration
- Mobile application API endpoints
- Analytics and reporting dashboard preparation

## Deployment Architecture

### Service Distribution

- Each service runs on dedicated ports (3000-3010)
- Environment-specific configuration
- Docker-ready application structure
- Health check endpoints implemented

### Monitoring & Logging

- Structured logging across services
- Request/response tracking
- Performance monitoring hooks
- Error tracking and alerting preparation

---

**Document Version:** 1.0
**Last Updated:** Current
**Status:** Active Development
**Contact:** Development Team
